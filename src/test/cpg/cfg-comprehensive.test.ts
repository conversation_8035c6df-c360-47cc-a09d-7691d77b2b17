/**
 * Comprehensive Control Flow Graph (CFG) Test Suite
 *
 * World-class testing for CFG generation covering all possible control flow scenarios:
 * - Basic sequential flow
 * - Conditional statements (if/else, ternary)
 * - Loop constructs (for, while, do-while)
 * - Exception handling (try/catch/finally)
 * - Function calls and returns
 * - Complex nested structures
 * - Edge cases and error conditions
 *
 * This test suite ensures CFG generation is modular, extensible, and handles
 * all Solidity control flow patterns with precision.
 */

import { describe, it, expect } from '@jest/globals';
import { SolidityAstService } from '../../services/solidity-ast.service';
import { solidityAstToCpg } from '../../cpg/solidity-transformer';
import { CpgGraph, CfgBlockNode } from '../../types/cpg';
import path from 'path';
import fs from 'fs';
import os from 'os';

/**
 * CFG Test Helper Class
 * Provides utilities for CFG analysis and validation
 */
class CfgTestHelper {
  static validateCfgStructure(cpg: CpgGraph): {
    cfgBlocks: CfgBlockNode[];
    entryBlocks: CfgBlockNode[];
    exitBlocks: CfgBlockNode[];
    basicBlocks: CfgBlockNode[];
    conditionalBlocks: CfgBlockNode[];
    loopBlocks: CfgBlockNode[];
    controlFlowEdges: any[];
  } {
    const cfgBlocks = Array.from(cpg.nodes.values()).filter(
      (node) => node.type === 'CFG_BLOCK'
    ) as CfgBlockNode[];

    const entryBlocks = cfgBlocks.filter(
      (block) => block.properties['blockType'] === 'ENTRY'
    );
    const exitBlocks = cfgBlocks.filter(
      (block) => block.properties['blockType'] === 'EXIT'
    );
    const basicBlocks = cfgBlocks.filter(
      (block) => block.properties['blockType'] === 'BASIC'
    );
    const conditionalBlocks = cfgBlocks.filter(
      (block) => block.properties['blockType'] === 'CONDITIONAL'
    );
    const loopBlocks = cfgBlocks.filter(
      (block) => block.properties['blockType'] === 'LOOP'
    );

    const controlFlowEdges = Array.from(cpg.edges.values()).filter(
      (edge) => edge.type === 'CONTROL_FLOW'
    );

    return {
      cfgBlocks,
      entryBlocks,
      exitBlocks,
      basicBlocks,
      conditionalBlocks,
      loopBlocks,
      controlFlowEdges,
    };
  }

  static async createAndParseCfgContract(
    contractCode: string,
    contractName: string
  ): Promise<CpgGraph | null> {
    try {
      const isAvailable = await SolidityAstService.isSolcAvailable();
      if (!isAvailable) {
        console.log(`⚠️ solc not available - skipping ${contractName} test`);
        return null;
      }

      const tempDir = os.tmpdir();
      const contractPath = path.join(tempDir, `${contractName}.sol`);
      fs.writeFileSync(contractPath, contractCode);

      const astResult = await SolidityAstService.parseContract(contractPath);
      if (!astResult.success) {
        console.log(`⚠️ Failed to parse ${contractName} - skipping test`);
        return null;
      }

      return solidityAstToCpg(astResult);
    } catch (error) {
      console.log(`⚠️ Error processing ${contractName} - skipping test`);
      return null;
    }
  }

  static validateCfgConnectivity(
    entryBlocks: CfgBlockNode[],
    exitBlocks: CfgBlockNode[],
    controlFlowEdges: any[]
  ): boolean {
    // Every entry block should have outgoing edges
    for (const entry of entryBlocks) {
      const hasOutgoing = controlFlowEdges.some(
        (edge) => edge.source === entry.id
      );
      if (!hasOutgoing) return false;
    }

    // Every exit block should have incoming edges (except for empty functions)
    for (const exit of exitBlocks) {
      const hasIncoming = controlFlowEdges.some(
        (edge) => edge.target === exit.id
      );
      if (!hasIncoming && controlFlowEdges.length > 0) return false;
    }

    return true;
  }
}

describe('Comprehensive CFG Generation', () => {
  describe('Basic Sequential Flow', () => {
    it('should generate correct CFG for simple sequential statements', async () => {
      const contractCode = `
        pragma solidity ^0.8.0;
        
        contract SequentialFlow {
          uint256 public value;
          
          function simpleSequence() public {
            value = 1;
            value = 2;
            value = 3;
          }
        }
      `;

      const cpg = await CfgTestHelper.createAndParseCfgContract(
        contractCode,
        'SequentialFlow'
      );
      if (!cpg) return;

      const cfg = CfgTestHelper.validateCfgStructure(cpg);

      console.log('🔍 Sequential Flow CFG Analysis:');
      console.log(`  Entry blocks: ${cfg.entryBlocks.length}`);
      console.log(`  Exit blocks: ${cfg.exitBlocks.length}`);
      console.log(`  Basic blocks: ${cfg.basicBlocks.length}`);
      console.log(`  Control flow edges: ${cfg.controlFlowEdges.length}`);

      // Validate structure
      expect(cfg.entryBlocks.length).toBeGreaterThan(0);
      expect(cfg.exitBlocks.length).toBeGreaterThan(0);
      expect(cfg.basicBlocks.length).toBeGreaterThan(0);
      expect(cfg.controlFlowEdges.length).toBeGreaterThan(0);

      // Validate connectivity
      expect(
        CfgTestHelper.validateCfgConnectivity(
          cfg.entryBlocks,
          cfg.exitBlocks,
          cfg.controlFlowEdges
        )
      ).toBe(true);
    });

    it('should handle empty functions correctly', async () => {
      const contractCode = `
        pragma solidity ^0.8.0;
        
        contract EmptyFunction {
          function emptyFunc() public pure {
            // Empty function body
          }
        }
      `;

      const cpg = await CfgTestHelper.createAndParseCfgContract(
        contractCode,
        'EmptyFunction'
      );
      if (!cpg) return;

      const cfg = CfgTestHelper.validateCfgStructure(cpg);

      console.log('🔍 Empty Function CFG Analysis:');
      console.log(`  Entry blocks: ${cfg.entryBlocks.length}`);
      console.log(`  Exit blocks: ${cfg.exitBlocks.length}`);
      console.log(`  Control flow edges: ${cfg.controlFlowEdges.length}`);

      // Even empty functions should have entry and exit blocks
      expect(cfg.entryBlocks.length).toBeGreaterThan(0);
      expect(cfg.exitBlocks.length).toBeGreaterThan(0);
    });
  });

  describe('Conditional Control Flow', () => {
    it('should generate correct CFG for if-else statements', async () => {
      const contractCode = `
        pragma solidity ^0.8.0;
        
        contract ConditionalFlow {
          uint256 public result;
          
          function ifElseFlow(uint256 x) public {
            if (x > 10) {
              result = x * 2;
            } else {
              result = x + 1;
            }
            result = result + 5; // Post-conditional statement
          }
        }
      `;

      const cpg = await CfgTestHelper.createAndParseCfgContract(
        contractCode,
        'ConditionalFlow'
      );
      if (!cpg) return;

      const cfg = CfgTestHelper.validateCfgStructure(cpg);

      console.log('🔍 Conditional Flow CFG Analysis:');
      console.log(`  Entry blocks: ${cfg.entryBlocks.length}`);
      console.log(`  Exit blocks: ${cfg.exitBlocks.length}`);
      console.log(`  Basic blocks: ${cfg.basicBlocks.length}`);
      console.log(`  Conditional blocks: ${cfg.conditionalBlocks.length}`);
      console.log(`  Control flow edges: ${cfg.controlFlowEdges.length}`);

      // Should have conditional structures
      expect(cfg.entryBlocks.length).toBeGreaterThan(0);
      expect(cfg.exitBlocks.length).toBeGreaterThan(0);
      expect(cfg.basicBlocks.length).toBeGreaterThan(0);
      expect(cfg.controlFlowEdges.length).toBeGreaterThan(2); // More complex flow

      // Validate connectivity
      expect(
        CfgTestHelper.validateCfgConnectivity(
          cfg.entryBlocks,
          cfg.exitBlocks,
          cfg.controlFlowEdges
        )
      ).toBe(true);
    });

    it('should handle nested if statements', async () => {
      const contractCode = `
        pragma solidity ^0.8.0;
        
        contract NestedConditionals {
          uint256 public result;
          
          function nestedIf(uint256 x, uint256 y) public {
            if (x > 0) {
              if (y > 0) {
                result = x + y;
              } else {
                result = x - y;
              }
            } else {
              result = 0;
            }
          }
        }
      `;

      const cpg = await CfgTestHelper.createAndParseCfgContract(
        contractCode,
        'NestedConditionals'
      );
      if (!cpg) return;

      const cfg = CfgTestHelper.validateCfgStructure(cpg);

      console.log('🔍 Nested Conditionals CFG Analysis:');
      console.log(`  Entry blocks: ${cfg.entryBlocks.length}`);
      console.log(`  Exit blocks: ${cfg.exitBlocks.length}`);
      console.log(`  Basic blocks: ${cfg.basicBlocks.length}`);
      console.log(`  Control flow edges: ${cfg.controlFlowEdges.length}`);

      // Nested structures should create more complex CFG
      expect(cfg.entryBlocks.length).toBeGreaterThan(0);
      expect(cfg.exitBlocks.length).toBeGreaterThan(0);
      expect(cfg.controlFlowEdges.length).toBeGreaterThan(3); // Complex nested flow

      // Validate connectivity
      expect(
        CfgTestHelper.validateCfgConnectivity(
          cfg.entryBlocks,
          cfg.exitBlocks,
          cfg.controlFlowEdges
        )
      ).toBe(true);
    });
  });

  describe('Loop Control Flow', () => {
    it('should generate correct CFG for for loops', async () => {
      const contractCode = `
        pragma solidity ^0.8.0;
        
        contract ForLoopFlow {
          uint256 public sum;
          
          function forLoopSum(uint256 n) public {
            sum = 0;
            for (uint256 i = 0; i < n; i++) {
              sum += i;
            }
            sum = sum * 2; // Post-loop statement
          }
        }
      `;

      const cpg = await CfgTestHelper.createAndParseCfgContract(
        contractCode,
        'ForLoopFlow'
      );
      if (!cpg) return;

      const cfg = CfgTestHelper.validateCfgStructure(cpg);

      console.log('🔍 For Loop CFG Analysis:');
      console.log(`  Entry blocks: ${cfg.entryBlocks.length}`);
      console.log(`  Exit blocks: ${cfg.exitBlocks.length}`);
      console.log(`  Basic blocks: ${cfg.basicBlocks.length}`);
      console.log(`  Loop blocks: ${cfg.loopBlocks.length}`);
      console.log(`  Control flow edges: ${cfg.controlFlowEdges.length}`);

      // Should have loop structures
      expect(cfg.entryBlocks.length).toBeGreaterThan(0);
      expect(cfg.exitBlocks.length).toBeGreaterThan(0);
      expect(cfg.basicBlocks.length).toBeGreaterThan(0);
      expect(cfg.controlFlowEdges.length).toBeGreaterThan(2); // Loop creates cycles

      // Validate connectivity
      expect(
        CfgTestHelper.validateCfgConnectivity(
          cfg.entryBlocks,
          cfg.exitBlocks,
          cfg.controlFlowEdges
        )
      ).toBe(true);
    });

    it('should handle while loops', async () => {
      const contractCode = `
        pragma solidity ^0.8.0;
        
        contract WhileLoopFlow {
          uint256 public counter;
          
          function whileLoop(uint256 limit) public {
            counter = 0;
            while (counter < limit) {
              counter++;
            }
          }
        }
      `;

      const cpg = await CfgTestHelper.createAndParseCfgContract(
        contractCode,
        'WhileLoopFlow'
      );
      if (!cpg) return;

      const cfg = CfgTestHelper.validateCfgStructure(cpg);

      console.log('🔍 While Loop CFG Analysis:');
      console.log(`  Entry blocks: ${cfg.entryBlocks.length}`);
      console.log(`  Exit blocks: ${cfg.exitBlocks.length}`);
      console.log(`  Basic blocks: ${cfg.basicBlocks.length}`);
      console.log(`  Control flow edges: ${cfg.controlFlowEdges.length}`);

      expect(cfg.entryBlocks.length).toBeGreaterThan(0);
      expect(cfg.exitBlocks.length).toBeGreaterThan(0);
      expect(cfg.controlFlowEdges.length).toBeGreaterThan(1);

      // Validate connectivity
      expect(
        CfgTestHelper.validateCfgConnectivity(
          cfg.entryBlocks,
          cfg.exitBlocks,
          cfg.controlFlowEdges
        )
      ).toBe(true);
    });
  });

  describe('Exception Handling Control Flow', () => {
    it('should generate correct CFG for try-catch statements', async () => {
      const contractCode = `
        pragma solidity ^0.8.0;

        contract ExceptionFlow {
          uint256 public result;

          function tryCatchFlow(address target) public {
            try this.riskyCall(target) returns (uint256 value) {
              result = value;
            } catch Error(string memory reason) {
              result = 0;
            } catch (bytes memory lowLevelData) {
              result = 1;
            }
            result = result + 10; // Post-exception statement
          }

          function riskyCall(address target) external pure returns (uint256) {
            require(target != address(0), "Invalid address");
            return 42;
          }
        }
      `;

      const cpg = await CfgTestHelper.createAndParseCfgContract(
        contractCode,
        'ExceptionFlow'
      );
      if (!cpg) return;

      const cfg = CfgTestHelper.validateCfgStructure(cpg);

      console.log('🔍 Exception Handling CFG Analysis:');
      console.log(`  Entry blocks: ${cfg.entryBlocks.length}`);
      console.log(`  Exit blocks: ${cfg.exitBlocks.length}`);
      console.log(`  Basic blocks: ${cfg.basicBlocks.length}`);
      console.log(`  Control flow edges: ${cfg.controlFlowEdges.length}`);

      expect(cfg.entryBlocks.length).toBeGreaterThan(0);
      expect(cfg.exitBlocks.length).toBeGreaterThan(0);
      expect(cfg.controlFlowEdges.length).toBeGreaterThan(2); // Exception paths

      // Validate connectivity
      expect(
        CfgTestHelper.validateCfgConnectivity(
          cfg.entryBlocks,
          cfg.exitBlocks,
          cfg.controlFlowEdges
        )
      ).toBe(true);
    });
  });

  describe('Complex Mixed Control Flow', () => {
    it('should handle complex mixed control structures', async () => {
      const contractCode = `
        pragma solidity ^0.8.0;

        contract ComplexFlow {
          uint256 public result;
          mapping(uint256 => uint256) public data;

          function complexControlFlow(uint256[] memory values) public {
            result = 0;

            // Nested loops with conditionals
            for (uint256 i = 0; i < values.length; i++) {
              if (values[i] > 100) {
                for (uint256 j = 0; j < 10; j++) {
                  if (j % 2 == 0) {
                    result += values[i] * j;
                  } else {
                    result -= j;
                  }
                }
              } else if (values[i] > 50) {
                uint256 temp = values[i];
                while (temp > 0) {
                  result += temp;
                  temp = temp / 2;
                  if (temp < 5) {
                    break;
                  }
                }
              } else {
                result += values[i];
              }
            }

            // Post-processing with exception handling
            try this.finalizeResult() {
              result = result * 2;
            } catch {
              result = 0;
            }
          }

          function finalizeResult() external view {
            require(result > 0, "Invalid result");
          }
        }
      `;

      const cpg = await CfgTestHelper.createAndParseCfgContract(
        contractCode,
        'ComplexFlow'
      );
      if (!cpg) return;

      const cfg = CfgTestHelper.validateCfgStructure(cpg);

      console.log('🔍 Complex Mixed Control Flow CFG Analysis:');
      console.log(`  Total CFG blocks: ${cfg.cfgBlocks.length}`);
      console.log(`  Entry blocks: ${cfg.entryBlocks.length}`);
      console.log(`  Exit blocks: ${cfg.exitBlocks.length}`);
      console.log(`  Basic blocks: ${cfg.basicBlocks.length}`);
      console.log(`  Conditional blocks: ${cfg.conditionalBlocks.length}`);
      console.log(`  Loop blocks: ${cfg.loopBlocks.length}`);
      console.log(`  Control flow edges: ${cfg.controlFlowEdges.length}`);

      // Complex flow should have many blocks and edges
      expect(cfg.cfgBlocks.length).toBeGreaterThan(10);
      expect(cfg.entryBlocks.length).toBeGreaterThan(0);
      expect(cfg.exitBlocks.length).toBeGreaterThan(0);
      expect(cfg.controlFlowEdges.length).toBeGreaterThan(10);

      // Validate connectivity
      expect(
        CfgTestHelper.validateCfgConnectivity(
          cfg.entryBlocks,
          cfg.exitBlocks,
          cfg.controlFlowEdges
        )
      ).toBe(true);
    });

    it('should handle early returns and multiple exit points', async () => {
      const contractCode = `
        pragma solidity ^0.8.0;

        contract MultipleExits {
          function multipleReturns(uint256 x) public pure returns (uint256) {
            if (x == 0) {
              return 0; // Early return 1
            }

            if (x == 1) {
              return 1; // Early return 2
            }

            for (uint256 i = 2; i <= x; i++) {
              if (i > 100) {
                return 999; // Early return 3
              }
            }

            return x * 2; // Normal return
          }
        }
      `;

      const cpg = await CfgTestHelper.createAndParseCfgContract(
        contractCode,
        'MultipleExits'
      );
      if (!cpg) return;

      const cfg = CfgTestHelper.validateCfgStructure(cpg);

      console.log('🔍 Multiple Exit Points CFG Analysis:');
      console.log(`  Entry blocks: ${cfg.entryBlocks.length}`);
      console.log(`  Exit blocks: ${cfg.exitBlocks.length}`);
      console.log(`  Basic blocks: ${cfg.basicBlocks.length}`);
      console.log(`  Control flow edges: ${cfg.controlFlowEdges.length}`);

      expect(cfg.entryBlocks.length).toBeGreaterThan(0);
      expect(cfg.exitBlocks.length).toBeGreaterThan(0);
      expect(cfg.controlFlowEdges.length).toBeGreaterThan(3); // Multiple paths to exit

      // Validate connectivity
      expect(
        CfgTestHelper.validateCfgConnectivity(
          cfg.entryBlocks,
          cfg.exitBlocks,
          cfg.controlFlowEdges
        )
      ).toBe(true);
    });
  });

  describe('CFG Edge Cases and Error Conditions', () => {
    it('should handle functions with only return statements', async () => {
      const contractCode = `
        pragma solidity ^0.8.0;

        contract ReturnOnly {
          function immediateReturn() public pure returns (uint256) {
            return 42;
          }
        }
      `;

      const cpg = await CfgTestHelper.createAndParseCfgContract(
        contractCode,
        'ReturnOnly'
      );
      if (!cpg) return;

      const cfg = CfgTestHelper.validateCfgStructure(cpg);

      console.log('🔍 Immediate Return CFG Analysis:');
      console.log(`  Entry blocks: ${cfg.entryBlocks.length}`);
      console.log(`  Exit blocks: ${cfg.exitBlocks.length}`);
      console.log(`  Control flow edges: ${cfg.controlFlowEdges.length}`);

      expect(cfg.entryBlocks.length).toBeGreaterThan(0);
      expect(cfg.exitBlocks.length).toBeGreaterThan(0);
    });

    it('should handle unreachable code detection', async () => {
      const contractCode = `
        pragma solidity ^0.8.0;

        contract UnreachableCode {
          uint256 public value;

          function unreachableCodeExample() public {
            value = 1;
            return;
            value = 2; // This should be unreachable
            value = 3; // This should also be unreachable
          }
        }
      `;

      const cpg = await CfgTestHelper.createAndParseCfgContract(
        contractCode,
        'UnreachableCode'
      );
      if (!cpg) return;

      const cfg = CfgTestHelper.validateCfgStructure(cpg);

      console.log('🔍 Unreachable Code CFG Analysis:');
      console.log(`  Entry blocks: ${cfg.entryBlocks.length}`);
      console.log(`  Exit blocks: ${cfg.exitBlocks.length}`);
      console.log(`  Basic blocks: ${cfg.basicBlocks.length}`);
      console.log(`  Control flow edges: ${cfg.controlFlowEdges.length}`);

      expect(cfg.entryBlocks.length).toBeGreaterThan(0);
      expect(cfg.exitBlocks.length).toBeGreaterThan(0);

      // Validate connectivity
      expect(
        CfgTestHelper.validateCfgConnectivity(
          cfg.entryBlocks,
          cfg.exitBlocks,
          cfg.controlFlowEdges
        )
      ).toBe(true);
    });
  });

  describe('CFG Modularity and Extensibility', () => {
    it('should maintain consistent CFG structure across different function types', async () => {
      const contractCode = `
        pragma solidity ^0.8.0;

        contract ModularCfg {
          uint256 public state;

          // Pure function
          function pureFunction(uint256 x) public pure returns (uint256) {
            return x * 2;
          }

          // View function
          function viewFunction() public view returns (uint256) {
            return state;
          }

          // Payable function
          function payableFunction() public payable {
            state = msg.value;
          }

          // Internal function
          function internalFunction(uint256 x) internal returns (uint256) {
            return x + 1;
          }

          // External function
          function externalFunction(uint256 x) external returns (uint256) {
            return internalFunction(x);
          }
        }
      `;

      const cpg = await CfgTestHelper.createAndParseCfgContract(
        contractCode,
        'ModularCfg'
      );
      if (!cpg) return;

      const cfg = CfgTestHelper.validateCfgStructure(cpg);

      console.log('🔍 Modular CFG Analysis:');
      console.log(`  Total functions detected: ${cfg.entryBlocks.length}`);
      console.log(`  Total CFG blocks: ${cfg.cfgBlocks.length}`);
      console.log(`  Control flow edges: ${cfg.controlFlowEdges.length}`);

      // Each function should have entry and exit blocks
      expect(cfg.entryBlocks.length).toBeGreaterThan(4); // At least 5 functions
      expect(cfg.exitBlocks.length).toBeGreaterThan(4);

      // Validate that each function has proper CFG structure
      expect(cfg.entryBlocks.length).toEqual(cfg.exitBlocks.length);

      // Validate connectivity for all functions
      expect(
        CfgTestHelper.validateCfgConnectivity(
          cfg.entryBlocks,
          cfg.exitBlocks,
          cfg.controlFlowEdges
        )
      ).toBe(true);
    });
  });
});

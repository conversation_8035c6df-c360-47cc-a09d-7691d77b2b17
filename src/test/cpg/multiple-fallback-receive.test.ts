/**
 * Multiple Fallback/Receive Functions Test Suite
 * Tests proper identification and distinction of fallback/receive functions across multiple contracts
 */

import path from 'path';
import { SolidityAstService } from '../../services/solidity-ast.service';
import { solidityAstToCpg } from '../../cpg/solidity-transformer';

describe('Multiple Fallback/Receive Function Identification', () => {
  const contractsDir = path.join(__dirname, '../test-contracts');
  const multipleContractPath = path.join(contractsDir, 'MultipleFallbackReceiveContract.sol');

  beforeAll(async () => {
    const isAvailable = await SolidityAstService.isSolcAvailable();
    if (!isAvailable) {
      console.warn('⚠️ Solc not available, skipping multiple fallback/receive tests');
    }
  });

  describe('Contract-Specific Function Identification', () => {
    it('should distinguish fallback functions across multiple contracts', async () => {
      const isAvailable = await SolidityAstService.isSolcAvailable();
      if (!isAvailable) return;

      const astResult = await SolidityAstService.parseContract(multipleContractPath);
      expect(astResult.success).toBe(true);
      if (!astResult.ast) return;

      const cpg = solidityAstToCpg(astResult);
      
      console.log('🔍 Testing Multiple Fallback Function Identification:');
      
      // Find all function nodes
      const functionNodes = Array.from(cpg.nodes.values()).filter(
        (node: any) => node.type === 'FUNCTION'
      );
      
      console.log(`  Total Functions Found: ${functionNodes.length}`);
      
      // Find all fallback functions
      const fallbackFunctions = functionNodes.filter((f: any) => 
        f.name === 'fallback' || f.properties?.isFallback
      );
      
      console.log(`  Fallback Functions Found: ${fallbackFunctions.length}`);
      
      // Should have multiple fallback functions (TokenA, TokenB, TokenC, OnlyFallback)
      expect(fallbackFunctions.length).toBeGreaterThanOrEqual(4);
      
      // Verify each fallback has unique signature
      const fallbackSignatures = fallbackFunctions.map((f: any) => f.properties?.signature);
      const uniqueFallbackSignatures = new Set(fallbackSignatures.filter(s => s));
      
      console.log('  Fallback Signatures:');
      fallbackSignatures.forEach((sig, index) => {
        console.log(`    [${index}]: ${sig || 'No signature'}`);
      });
      
      // Each fallback should have a unique signature due to contract context
      expect(uniqueFallbackSignatures.size).toBe(fallbackFunctions.length);
      
      // Verify signatures contain contract names
      const signaturesWithContracts = fallbackSignatures.filter(sig => 
        sig && (sig.includes('TokenA') || sig.includes('TokenB') || sig.includes('TokenC') || sig.includes('OnlyFallback'))
      );
      expect(signaturesWithContracts.length).toBeGreaterThanOrEqual(4);
    });

    it('should distinguish receive functions across multiple contracts', async () => {
      const isAvailable = await SolidityAstService.isSolcAvailable();
      if (!isAvailable) return;

      const astResult = await SolidityAstService.parseContract(multipleContractPath);
      expect(astResult.success).toBe(true);
      if (!astResult.ast) return;

      const cpg = solidityAstToCpg(astResult);
      
      console.log('🔍 Testing Multiple Receive Function Identification:');
      
      const functionNodes = Array.from(cpg.nodes.values()).filter(
        (node: any) => node.type === 'FUNCTION'
      );
      
      // Find all receive functions
      const receiveFunctions = functionNodes.filter((f: any) => 
        f.name === 'receive' || f.properties?.isReceive
      );
      
      console.log(`  Receive Functions Found: ${receiveFunctions.length}`);
      
      // Should have multiple receive functions (TokenA, TokenB, TokenC, OnlyReceive)
      expect(receiveFunctions.length).toBeGreaterThanOrEqual(4);
      
      // Verify each receive has unique signature
      const receiveSignatures = receiveFunctions.map((f: any) => f.properties?.signature);
      const uniqueReceiveSignatures = new Set(receiveSignatures.filter(s => s));
      
      console.log('  Receive Signatures:');
      receiveSignatures.forEach((sig, index) => {
        console.log(`    [${index}]: ${sig || 'No signature'}`);
      });
      
      // Each receive should have a unique signature due to contract context
      expect(uniqueReceiveSignatures.size).toBe(receiveFunctions.length);
      
      // Verify signatures contain contract names
      const signaturesWithContracts = receiveSignatures.filter(sig => 
        sig && (sig.includes('TokenA') || sig.includes('TokenB') || sig.includes('TokenC') || sig.includes('OnlyReceive'))
      );
      expect(signaturesWithContracts.length).toBeGreaterThanOrEqual(4);
    });

    it('should handle contracts with only fallback or only receive', async () => {
      const isAvailable = await SolidityAstService.isSolcAvailable();
      if (!isAvailable) return;

      const astResult = await SolidityAstService.parseContract(multipleContractPath);
      expect(astResult.success).toBe(true);
      if (!astResult.ast) return;

      const cpg = solidityAstToCpg(astResult);
      
      console.log('🔍 Testing Contracts with Only Fallback or Only Receive:');
      
      const functionNodes = Array.from(cpg.nodes.values()).filter(
        (node: any) => node.type === 'FUNCTION'
      );
      
      // Find OnlyFallback contract functions
      const onlyFallbackFunctions = functionNodes.filter((f: any) => 
        f.properties?.signature?.includes('OnlyFallback')
      );
      
      // Find OnlyReceive contract functions  
      const onlyReceiveFunctions = functionNodes.filter((f: any) => 
        f.properties?.signature?.includes('OnlyReceive')
      );
      
      console.log(`  OnlyFallback Functions: ${onlyFallbackFunctions.length}`);
      console.log(`  OnlyReceive Functions: ${onlyReceiveFunctions.length}`);
      
      // OnlyFallback should have fallback but no receive
      const onlyFallbackHasFallback = onlyFallbackFunctions.some((f: any) => 
        f.name === 'fallback' || f.properties?.isFallback
      );
      const onlyFallbackHasReceive = onlyFallbackFunctions.some((f: any) => 
        f.name === 'receive' || f.properties?.isReceive
      );
      
      expect(onlyFallbackHasFallback).toBe(true);
      expect(onlyFallbackHasReceive).toBe(false);
      
      // OnlyReceive should have receive but no fallback
      const onlyReceiveHasReceive = onlyReceiveFunctions.some((f: any) => 
        f.name === 'receive' || f.properties?.isReceive
      );
      const onlyReceiveHasFallback = onlyReceiveFunctions.some((f: any) => 
        f.name === 'fallback' || f.properties?.isFallback
      );
      
      expect(onlyReceiveHasReceive).toBe(true);
      expect(onlyReceiveHasFallback).toBe(false);
    });

    it('should handle contracts with neither fallback nor receive', async () => {
      const isAvailable = await SolidityAstService.isSolcAvailable();
      if (!isAvailable) return;

      const astResult = await SolidityAstService.parseContract(multipleContractPath);
      expect(astResult.success).toBe(true);
      if (!astResult.ast) return;

      const cpg = solidityAstToCpg(astResult);
      
      const functionNodes = Array.from(cpg.nodes.values()).filter(
        (node: any) => node.type === 'FUNCTION'
      );
      
      // Find NoFallbackReceive contract functions
      const noFallbackReceiveFunctions = functionNodes.filter((f: any) => 
        f.properties?.signature?.includes('NoFallbackReceive')
      );
      
      console.log(`  NoFallbackReceive Functions: ${noFallbackReceiveFunctions.length}`);
      
      // Should have regular functions but no fallback/receive
      const hasFallback = noFallbackReceiveFunctions.some((f: any) => 
        f.name === 'fallback' || f.properties?.isFallback
      );
      const hasReceive = noFallbackReceiveFunctions.some((f: any) => 
        f.name === 'receive' || f.properties?.isReceive
      );
      
      expect(hasFallback).toBe(false);
      expect(hasReceive).toBe(false);
      expect(noFallbackReceiveFunctions.length).toBeGreaterThan(0); // Should have setValue function
    });
  });

  describe('Function Registry Integration', () => {
    it('should properly register and lookup fallback/receive functions', async () => {
      const isAvailable = await SolidityAstService.isSolcAvailable();
      if (!isAvailable) return;

      const astResult = await SolidityAstService.parseContract(multipleContractPath);
      expect(astResult.success).toBe(true);
      if (!astResult.ast) return;

      const cpg = solidityAstToCpg(astResult);
      
      console.log('🔍 Testing Function Registry Integration:');
      
      // All functions should have unique IDs
      const functionNodes = Array.from(cpg.nodes.values()).filter(
        (node: any) => node.type === 'FUNCTION'
      );
      
      const functionIds = functionNodes.map((f: any) => f.id);
      const uniqueIds = new Set(functionIds);
      
      expect(uniqueIds.size).toBe(functionNodes.length);
      console.log(`  All ${functionNodes.length} functions have unique IDs ✅`);
      
      // All functions should have signatures
      const functionsWithSignatures = functionNodes.filter((f: any) => 
        f.properties?.signature
      );
      
      expect(functionsWithSignatures.length).toBe(functionNodes.length);
      console.log(`  All ${functionNodes.length} functions have signatures ✅`);
      
      // No signature collisions
      const signatures = functionNodes.map((f: any) => f.properties?.signature);
      const uniqueSignatures = new Set(signatures);
      
      expect(uniqueSignatures.size).toBe(functionNodes.length);
      console.log(`  All ${functionNodes.length} functions have unique signatures ✅`);
    });
  });
});

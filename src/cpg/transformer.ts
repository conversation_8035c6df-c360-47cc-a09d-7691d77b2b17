/**
 * CPG Transformer - Converts Slither AST to Code Property Graph
 * Supports deep state management, taint analysis, and control flow
 */

import { SlitherAstResult } from '../types/ast';
import {
  CpgGraph,
  CpgNode,
  CpgEdge,
  VariableNode,
  CpgMetadata,
  AnalysisContext,
  TaintFlow,
  SinkInfo,
} from '../types/cpg';

// Import Slither-specific processors
import { BaseProcessor, ProcessorContext } from './processors/base-processor';
import { SlitherContractProcessor } from './processors/slither-contract-processor';
import { SlitherFunctionProcessor } from './processors/slither-function-processor';
import { SlitherVariableProcessor } from './processors/slither-variable-processor';
import { SlitherExpressionProcessor } from './processors/slither-expression-processor';
import { FunctionRegistry } from './function-registry';

export class CpgTransformer {
  private nodes: Map<string, CpgNode> = new Map();
  private edges: Map<string, CpgEdge> = new Map();
  private context: AnalysisContext = {
    variableScopes: new Map(),
    taintedVariables: new Set(),
    stateVariables: new Set(),
  };
  private nodeIdCounter = 0;
  private edgeIdCounter = 0;
  private processors: BaseProcessor[] = [];
  private processorContext!: ProcessorContext;
  private functionRegistry: FunctionRegistry = new FunctionRegistry();

  constructor() {
    this.initializeProcessors();
  }

  /**
   * Initialize Slither-specific processors
   */
  private initializeProcessors(): void {
    this.processorContext = {
      generateNodeId: () => this.generateNodeId(),
      generateEdgeId: () => this.generateEdgeId(),
      extractSourceLocation: (_node, _filePath) => {
        // For Slither, we don't have standard AST nodes, so this is a placeholder
        return undefined;
      },
      getAnalysisContext: () => this.context,
      updateAnalysisContext: (context) => {
        this.context = { ...this.context, ...context };
      },
      addNode: (node) => this.nodes.set(node.id, node),
      addEdge: (edge) => this.edges.set(edge.id, edge),
      getNode: (id) => this.nodes.get(id),
      getFunctionRegistry: () => this.functionRegistry,
    };

    // Initialize Slither processors in priority order
    this.processors = [
      new SlitherContractProcessor(this.processorContext), // 100
      new SlitherFunctionProcessor(this.processorContext), // 80
      new SlitherVariableProcessor(this.processorContext), // 70
      new SlitherExpressionProcessor(this.processorContext), // 50
    ];
  }

  /**
   * Get the appropriate processor for a given Slither element type
   */
  private getProcessorForElement(elementType: string): BaseProcessor | null {
    return (
      this.processors.find((processor) => processor.canProcess(elementType)) ||
      null
    );
  }

  /**
   * Transform Slither AST to CPG
   */
  public astToCpg(ast: SlitherAstResult): CpgGraph {
    this.reset();

    if (!ast.success || !ast.ast) {
      throw new Error(`Invalid AST result: ${ast.error || 'Unknown error'}`);
    }

    // Parse the actual Slither JSON structure
    const slitherData = ast.ast as any;

    if (!slitherData.results || !slitherData.results.detectors) {
      throw new Error(
        'Invalid Slither AST structure: missing results.detectors'
      );
    }

    // Extract contracts, functions, and variables from detector elements
    this.extractFromDetectors(slitherData.results.detectors, ast.filePath);

    // Note: Advanced analysis (CFG, taint analysis, call graph, data dependency)
    // is now handled by the SolidityCpgTransformer with dedicated analyzers

    const metadata = this.buildMetadata(ast.filePath, slitherData);

    return {
      nodes: this.nodes,
      edges: this.edges,
      metadata,
    };
  }

  private reset(): void {
    this.nodes.clear();
    this.edges.clear();
    this.context = {
      variableScopes: new Map(),
      taintedVariables: new Set(),
      stateVariables: new Set(),
    };
    this.nodeIdCounter = 0;
    this.edgeIdCounter = 0;
    this.functionRegistry.clear();
  }

  private generateNodeId(): string {
    return `node_${++this.nodeIdCounter}`;
  }

  private generateEdgeId(): string {
    return `edge_${++this.edgeIdCounter}`;
  }

  private extractFromDetectors(detectors: any[], filePath: string): void {
    const contractMap = new Map<string, string>(); // contract name -> node id
    const functionMap = new Map<string, string>(); // function signature -> node id

    for (const detector of detectors) {
      if (!detector.elements) continue;

      for (const element of detector.elements) {
        this.processElement(element, contractMap, functionMap, filePath);
      }
    }

    // Perform taint analysis after all nodes are created
    this.performTaintAnalysis();
  }

  private processElement(
    element: any,
    contractMap: Map<string, string>,
    functionMap: Map<string, string>,
    filePath: string
  ): void {
    // Skip pragma elements for now
    if (element.type === 'pragma') {
      return;
    }

    // Try to find a processor for this element type
    const processor = this.getProcessorForElement(element.type);

    if (processor) {
      console.log(`🔧 Using processor for Slither element: ${element.type}`);

      // Use the specialized processor
      let result;
      if (processor instanceof SlitherContractProcessor) {
        result = processor.processSlitherElement(
          element,
          filePath,
          contractMap
        );
      } else if (processor instanceof SlitherFunctionProcessor) {
        result = processor.processSlitherElement(
          element,
          filePath,
          contractMap,
          functionMap
        );
      } else if (processor instanceof SlitherVariableProcessor) {
        result = processor.processSlitherElement(
          element,
          filePath,
          contractMap,
          functionMap
        );
      } else if (processor instanceof SlitherExpressionProcessor) {
        result = processor.processSlitherElement(element, filePath);
      } else {
        console.warn(`⚠️ Unknown processor type for element: ${element.type}`);
        return;
      }

      // Add the node and edges to our collections
      this.nodes.set(result.node.id, result.node);
      result.edges.forEach((edge: CpgEdge) => {
        this.edges.set(edge.id, edge);
      });
    } else {
      console.warn(
        `⚠️ No processor found for Slither element type: ${element.type}`
      );
      // Fallback to legacy processing
      this.processElementLegacy(element, contractMap, functionMap, filePath);
    }
  }

  /**
   * Legacy processing for elements when processors are not available
   * NOTE: This should NEVER be used as all element types must have processors
   */
  private processElementLegacy(
    element: any,
    _contractMap: Map<string, string>,
    _functionMap: Map<string, string>,
    _filePath: string
  ): void {
    // All element types should now be handled by processors
    // If we reach here, it means a processor is missing for this element type
    console.error(
      `❌ CRITICAL: No processor found for element type: ${element.type}. This indicates a missing processor and violates our architecture.`
    );

    // Instead of creating nodes directly, we should throw an error
    // This forces us to create proper processors for all element types
    throw new Error(
      `Missing processor for element type: ${element.type}. Please implement a processor for this element type.`
    );
  }

  // REMOVED: processContractLegacy - Use SlitherContractProcessor instead

  // REMOVED: processFunctionLegacy - Use SlitherFunctionProcessor instead

  // REMOVED: processVariableLegacy - Use SlitherVariableProcessor instead

  // REMOVED: processExpressionLegacy - Use SlitherExpressionProcessor instead

  private performTaintAnalysis(): void {
    // Identify taint sources (parameters, external calls)
    const taintSources = new Set<string>();
    const stateSinks = new Set<string>();

    for (const [nodeId, node] of this.nodes) {
      if (node.type === 'VARIABLE') {
        const variable = node as VariableNode;

        // Mark parameters as taint sources
        if (variable.properties.scope === 'PARAMETER') {
          taintSources.add(nodeId);
          variable.properties.isTainted = true;
          variable.properties.taintSource = 'user_input';
        }

        // Mark state variables as potential sinks
        if (variable.properties.isStateVariable) {
          stateSinks.add(nodeId);
        }
      }
    }

    // Create taint edges from sources to sinks
    this.createTaintFlows(taintSources, stateSinks);

    // Note: Advanced taint analysis (inter-procedural flows, complex expressions,
    // function call tracking, points-to analysis) is now handled by TaintAnalyzer
  }

  // REMOVED: createTaintFlows - Use TaintAnalyzer from analyzers instead
  private createTaintFlows(_sources: Set<string>, _sinks: Set<string>): void {
    // Note: Taint flow creation is now handled by TaintAnalyzer in SolidityCpgTransformer
    console.log('⚠️ Taint flow creation should be handled by TaintAnalyzer');
  }

  // REMOVED: extractSourceLocation - Use processor context method instead

  private buildMetadata(filePath: string, slitherData: any): CpgMetadata {
    const nodesByType = new Map<string, number>();
    for (const node of this.nodes.values()) {
      nodesByType.set(node.type, (nodesByType.get(node.type) || 0) + 1);
    }

    // Extract taint flows from TAINT edges
    const taintFlows: TaintFlow[] = [];
    const sinks: SinkInfo[] = [];

    for (const edge of this.edges.values()) {
      if (edge.type === 'TAINT') {
        const sourceNode = this.nodes.get(edge.source);
        const targetNode = this.nodes.get(edge.target);

        if (sourceNode && targetNode) {
          taintFlows.push({
            source: sourceNode.name,
            sink: targetNode.name,
            path: [sourceNode.id, targetNode.id],
          });

          // Add sink information
          if (
            targetNode.type === 'VARIABLE' &&
            (targetNode as VariableNode).properties.isStateVariable
          ) {
            sinks.push({
              nodeId: targetNode.id,
              sinkType: 'STATE_WRITE',
              location: targetNode.sourceLocation || {
                start: 0,
                length: 0,
                lines: [],
                startColumn: 0,
                endColumn: 0,
                filename: filePath,
              },
              taintSources: [sourceNode.name],
            });
          }
        }
      }
    }

    return {
      sourceFile: filePath,
      timestamp: Date.now(),
      slitherVersion: slitherData.version,
      nodeCount: this.nodes.size,
      edgeCount: this.edges.size,
      contractCount: nodesByType.get('CONTRACT') || 0,
      functionCount: nodesByType.get('FUNCTION') || 0,
      variableCount: nodesByType.get('VARIABLE') || 0,
      taintFlows,
      sinks,
    };
  }
}

/**
 * Main export function for transforming AST to CPG
 */
export function astToCpg(ast: SlitherAstResult): CpgGraph {
  const transformer = new CpgTransformer();
  return transformer.astToCpg(ast);
}

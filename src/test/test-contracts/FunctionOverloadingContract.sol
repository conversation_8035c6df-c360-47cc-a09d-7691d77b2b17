// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

/**
 * Function Overloading Test Contract
 * Tests comprehensive function overloading scenarios for CPG function identification
 */
contract FunctionOverloadingContract {
    uint256 public value;
    string public text;
    bytes public data;
    mapping(address => uint256) public balances;

    event FunctionCalled(string functionSignature, uint256 paramCount);

    // Constructor
    constructor() {
        value = 100;
        text = "initial";
    }

    // Basic function overloading - same name, different parameter counts
    function setValue() external {
        value = 0;
        emit FunctionCalled("setValue()", 0);
    }

    function setValue(uint256 _value) external {
        value = _value;
        emit FunctionCalled("setValue(uint256)", 1);
    }

    function setValue(uint256 _value, string memory _text) external {
        value = _value;
        text = _text;
        emit FunctionCalled("setValue(uint256,string)", 2);
    }

    function setValue(uint256 _value, string memory _text, bytes memory _data) external {
        value = _value;
        text = _text;
        data = _data;
        emit FunctionCalled("setValue(uint256,string,bytes)", 3);
    }

    // Type-based overloading - same name, different parameter types
    function process(uint256 number) external returns (uint256) {
        emit FunctionCalled("process(uint256)", 1);
        return number * 2;
    }

    function process(string memory str) external returns (string memory) {
        emit FunctionCalled("process(string)", 1);
        return string(abi.encodePacked(str, "_processed"));
    }

    function process(bytes memory data_) external returns (bytes memory) {
        emit FunctionCalled("process(bytes)", 1);
        return abi.encodePacked(data_, hex"deadbeef");
    }

    function process(address addr) external returns (address) {
        emit FunctionCalled("process(address)", 1);
        balances[addr] = 1000;
        return addr;
    }

    // Complex type overloading
    function transfer(address to, uint256 amount) external returns (bool) {
        emit FunctionCalled("transfer(address,uint256)", 2);
        balances[msg.sender] -= amount;
        balances[to] += amount;
        return true;
    }

    function transfer(address[] memory recipients, uint256[] memory amounts) external returns (bool) {
        emit FunctionCalled("transfer(address[],uint256[])", 2);
        require(recipients.length == amounts.length, "Array length mismatch");
        
        for (uint256 i = 0; i < recipients.length; i++) {
            balances[msg.sender] -= amounts[i];
            balances[recipients[i]] += amounts[i];
        }
        return true;
    }

    function transfer(address to, uint256 amount, bytes memory data_) external returns (bool) {
        emit FunctionCalled("transfer(address,uint256,bytes)", 3);
        balances[msg.sender] -= amount;
        balances[to] += amount;
        // Additional logic with data
        return true;
    }

    // Struct-based overloading
    struct UserInfo {
        address user;
        uint256 balance;
        string name;
    }

    function updateUser(address user, uint256 balance) external {
        emit FunctionCalled("updateUser(address,uint256)", 2);
        balances[user] = balance;
    }

    function updateUser(UserInfo memory userInfo) external {
        emit FunctionCalled("updateUser(UserInfo)", 1);
        balances[userInfo.user] = userInfo.balance;
        // Additional logic with user info
    }

    function updateUser(UserInfo[] memory users) external {
        emit FunctionCalled("updateUser(UserInfo[])", 1);
        for (uint256 i = 0; i < users.length; i++) {
            balances[users[i].user] = users[i].balance;
        }
    }

    // Visibility-based functions (not overloading, but different functions)
    function internalFunction() internal pure returns (string memory) {
        return "internal";
    }

    function privateFunction() private pure returns (string memory) {
        return "private";
    }

    function publicFunction() public pure returns (string memory) {
        return "public";
    }

    function externalFunction() external pure returns (string memory) {
        return "external";
    }

    // State mutability variations (not overloading, but different functions)
    function viewFunction() external view returns (uint256) {
        return value;
    }

    function pureFunction() external pure returns (uint256) {
        return 42;
    }

    function payableFunction() external payable returns (uint256) {
        return msg.value;
    }

    // Modifier-based functions
    modifier onlyPositive(uint256 amount) {
        require(amount > 0, "Amount must be positive");
        _;
    }

    modifier onlyOwner() {
        require(msg.sender == address(this), "Not owner");
        _;
    }

    function modifiedFunction(uint256 amount) external onlyPositive(amount) {
        value = amount;
        emit FunctionCalled("modifiedFunction(uint256)", 1);
    }

    function modifiedFunction(uint256 amount, string memory reason) external onlyPositive(amount) onlyOwner {
        value = amount;
        text = reason;
        emit FunctionCalled("modifiedFunction(uint256,string)", 2);
    }

    // Special functions
    receive() external payable {
        emit FunctionCalled("receive()", 0);
    }

    fallback() external payable {
        emit FunctionCalled("fallback()", 0);
    }

    // Return type variations (not overloading in Solidity, but different signatures)
    function calculate(uint256 a, uint256 b) external pure returns (uint256 sum) {
        sum = a + b;
    }

    function calculateMultiple(uint256 a, uint256 b) external pure returns (uint256 sum, uint256 product, uint256 difference) {
        sum = a + b;
        product = a * b;
        difference = a > b ? a - b : b - a;
    }

    // Generic function names that might cause conflicts
    function execute() external {
        emit FunctionCalled("execute()", 0);
    }

    function execute(bytes memory data_) external {
        emit FunctionCalled("execute(bytes)", 1);
        // Execute arbitrary data
    }

    function execute(address target, bytes memory data_) external {
        emit FunctionCalled("execute(address,bytes)", 2);
        // Execute on target
    }

    function execute(address target, uint256 value_, bytes memory data_) external {
        emit FunctionCalled("execute(address,uint256,bytes)", 3);
        // Execute on target with value
    }

    // Getter functions for testing
    function getFunctionCallCount() external view returns (uint256) {
        return value; // Using value as a simple counter
    }

    function getStoredData() external view returns (uint256, string memory, bytes memory) {
        return (value, text, data);
    }
}

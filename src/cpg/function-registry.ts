/**
 * Function Registry System
 * Provides robust function identification, storage, and retrieval for CFG generation
 */

import { SolidityAstNode } from '../services/solidity-ast.service';
import { FunctionNode } from '../types/cpg';

export interface FunctionIdentifier {
  /** Unique function ID for storage and retrieval */
  id: string;
  /** Contract-qualified function signature */
  signature: string;
  /** Contract name where function is defined */
  contractName: string;
  /** Function name (or special identifier for anonymous functions) */
  functionName: string;
  /** Parameter types for overloading resolution */
  parameterTypes: string[];
  /** Function kind (function, constructor, fallback, receive, modifier) */
  kind: FunctionKind;
  /** Inheritance level (0 = base, higher = more derived) */
  inheritanceLevel: number;
  /** Whether this function overrides another */
  isOverride: boolean;
  /** Whether this function is virtual */
  isVirtual: boolean;
}

export type FunctionKind =
  | 'function'
  | 'constructor'
  | 'fallback'
  | 'receive'
  | 'modifier'
  | 'getter'
  | 'setter';

export interface StoredFunction {
  identifier: FunctionIdentifier;
  astNode: SolidityAstNode;
  cpgNode: FunctionNode;
  filePath: string;
}

export class FunctionRegistry {
  private functions: Map<string, StoredFunction> = new Map();
  private signatureToId: Map<string, string> = new Map();
  private contractFunctions: Map<string, Set<string>> = new Map();
  private overloadGroups: Map<string, string[]> = new Map();
  private signatureCounter: Map<string, number> = new Map(); // Track signature occurrences

  /**
   * Register a function with enhanced identification
   */
  registerFunction(
    astNode: SolidityAstNode,
    cpgNode: FunctionNode,
    filePath: string,
    contractName: string,
    inheritanceLevel: number = 0
  ): FunctionIdentifier {
    const identifier = this.buildFunctionIdentifier(
      astNode,
      contractName,
      inheritanceLevel
    );

    const storedFunction: StoredFunction = {
      identifier,
      astNode,
      cpgNode,
      filePath,
    };

    // Store function
    this.functions.set(identifier.id, storedFunction);
    this.signatureToId.set(identifier.signature, identifier.id);

    // Track contract functions
    if (!this.contractFunctions.has(contractName)) {
      this.contractFunctions.set(contractName, new Set());
    }
    this.contractFunctions.get(contractName)!.add(identifier.id);

    // Track overload groups
    const overloadKey = `${contractName}.${identifier.functionName}`;
    if (!this.overloadGroups.has(overloadKey)) {
      this.overloadGroups.set(overloadKey, []);
    }
    this.overloadGroups.get(overloadKey)!.push(identifier.id);

    return identifier;
  }

  /**
   * Build comprehensive function identifier
   */
  private buildFunctionIdentifier(
    astNode: SolidityAstNode,
    contractName: string,
    inheritanceLevel: number
  ): FunctionIdentifier {
    const kind = this.determineFunctionKind(astNode);
    const functionName = this.extractFunctionName(astNode, kind);
    const parameterTypes = this.extractParameterTypes(astNode);

    // Build unique signature with contract context
    const baseSignature = this.buildEnhancedSignature(
      contractName,
      functionName,
      parameterTypes,
      kind,
      inheritanceLevel
    );

    // Make signature unique by adding counter for duplicates
    const signature = this.makeSignatureUnique(baseSignature);

    // Generate unique ID
    const id = this.generateFunctionId(signature, kind);

    return {
      id,
      signature,
      contractName,
      functionName,
      parameterTypes,
      kind,
      inheritanceLevel,
      isOverride: this.isOverrideFunction(astNode),
      isVirtual: this.isVirtualFunction(astNode),
    };
  }

  /**
   * Determine function kind from AST node
   */
  private determineFunctionKind(astNode: SolidityAstNode): FunctionKind {
    const nodeAny = astNode as any;

    // Check for constructor
    if (nodeAny['kind'] === 'constructor' || astNode.name === 'constructor') {
      return 'constructor';
    }

    // Check for fallback
    if (nodeAny['kind'] === 'fallback' || astNode.name === 'fallback') {
      return 'fallback';
    }

    // Check for receive
    if (nodeAny['kind'] === 'receive' || astNode.name === 'receive') {
      return 'receive';
    }

    // Check for modifier
    if (astNode.nodeType === 'ModifierDefinition') {
      return 'modifier';
    }

    // Check for getter/setter patterns
    if (astNode.name?.startsWith('get') && astNode.stateMutability === 'view') {
      return 'getter';
    }
    if (astNode.name?.startsWith('set') && astNode.stateMutability !== 'view') {
      return 'setter';
    }

    return 'function';
  }

  /**
   * Extract function name with special handling for anonymous functions
   */
  private extractFunctionName(
    astNode: SolidityAstNode,
    kind: FunctionKind
  ): string {
    if (kind === 'constructor') {
      return 'constructor';
    }
    if (kind === 'fallback') {
      return 'fallback';
    }
    if (kind === 'receive') {
      return 'receive';
    }

    // Use actual name or generate unique name for anonymous functions
    if (astNode.name) {
      return astNode.name;
    }

    // Generate unique name for anonymous functions
    return `anonymous_${kind}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Extract parameter types for function signature
   */
  private extractParameterTypes(astNode: SolidityAstNode): string[] {
    if (!astNode.parameters?.parameters) {
      return [];
    }

    return astNode.parameters.parameters['map']((param: any) => {
      return param.typeDescriptions?.typeString || 'unknown';
    });
  }

  /**
   * Build enhanced function signature with contract context
   */
  private buildEnhancedSignature(
    contractName: string,
    functionName: string,
    parameterTypes: string[],
    kind: FunctionKind,
    inheritanceLevel: number
  ): string {
    const params = parameterTypes.join(',');
    const kindPrefix = kind !== 'function' ? `${kind}:` : '';
    const inheritanceSuffix =
      inheritanceLevel > 0 ? `@${inheritanceLevel}` : '';

    return `${contractName}.${kindPrefix}${functionName}(${params})${inheritanceSuffix}`;
  }

  /**
   * Make signature unique by adding counter for duplicates
   */
  private makeSignatureUnique(baseSignature: string): string {
    // Check if this signature already exists
    const currentCount = this.signatureCounter.get(baseSignature) || 0;

    // Increment counter for this signature
    this.signatureCounter.set(baseSignature, currentCount + 1);

    // If this is the first occurrence, use the base signature
    if (currentCount === 0) {
      return baseSignature;
    }

    // For subsequent occurrences, add a unique suffix
    return `${baseSignature}#${currentCount + 1}`;
  }

  /**
   * Generate unique function ID
   */
  private generateFunctionId(signature: string, kind: FunctionKind): string {
    const hash = this.simpleHash(signature);
    return `func_${kind}_${hash}`;
  }

  /**
   * Simple hash function for generating IDs
   */
  private simpleHash(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * Check if function is virtual
   */
  private isVirtualFunction(astNode: SolidityAstNode): boolean {
    const nodeAny = astNode as any;
    return Boolean(
      nodeAny['virtual'] === true ||
        (astNode.modifiers &&
          astNode.modifiers.some((m: any) => m.name === 'virtual'))
    );
  }

  /**
   * Check if function is an override
   */
  private isOverrideFunction(astNode: SolidityAstNode): boolean {
    const nodeAny = astNode as any;
    return Boolean(
      nodeAny['override'] !== undefined ||
        (astNode.modifiers &&
          astNode.modifiers.some((m: any) => m.name === 'override')) ||
        (nodeAny['overrides'] && nodeAny['overrides'].length > 0) ||
        (nodeAny['baseFunctions'] && nodeAny['baseFunctions'].length > 0)
    );
  }

  /**
   * Lookup functions by various criteria
   */
  getFunctionById(id: string): StoredFunction | undefined {
    return this.functions.get(id);
  }

  getFunctionBySignature(signature: string): StoredFunction | undefined {
    const id = this.signatureToId.get(signature);
    return id ? this.functions.get(id) : undefined;
  }

  getFunctionsByContract(contractName: string): StoredFunction[] {
    const functionIds = this.contractFunctions.get(contractName) || new Set();
    return Array.from(functionIds)
      .map((id) => this.functions.get(id))
      .filter((f): f is StoredFunction => f !== undefined);
  }

  getOverloadedFunctions(
    contractName: string,
    functionName: string
  ): StoredFunction[] {
    const overloadKey = `${contractName}.${functionName}`;
    const functionIds = this.overloadGroups.get(overloadKey) || [];
    return functionIds
      .map((id) => this.functions.get(id))
      .filter((f): f is StoredFunction => f !== undefined);
  }

  /**
   * Find function for CFG generation
   */
  findFunctionForCfg(
    contractName: string,
    functionName: string,
    parameterTypes?: string[]
  ): StoredFunction | undefined {
    // Try exact match first
    if (parameterTypes) {
      const signature = this.buildEnhancedSignature(
        contractName,
        functionName,
        parameterTypes,
        'function',
        0
      );
      const exact = this.getFunctionBySignature(signature);
      if (exact) return exact;
    }

    // Try overloaded functions
    const overloaded = this.getOverloadedFunctions(contractName, functionName);
    if (overloaded.length === 1) {
      return overloaded[0];
    }

    // If multiple overloads and no parameter types, return the first one
    // (CFG generation should work with any overload)
    if (overloaded.length > 0) {
      console.warn(
        `Multiple overloads found for ${contractName}.${functionName}, using first one for CFG`
      );
      return overloaded[0];
    }

    return undefined;
  }

  /**
   * Get all functions for debugging
   */
  getAllFunctions(): StoredFunction[] {
    return Array.from(this.functions.values());
  }

  /**
   * Clear registry
   */
  clear(): void {
    this.functions.clear();
    this.signatureToId.clear();
    this.contractFunctions.clear();
    this.overloadGroups.clear();
    this.signatureCounter.clear();
  }
}

/**
 * Library Processor
 * Handles LibraryDefinition AST nodes
 */

import { BaseProcessor, ProcessorResult } from './base-processor';
import { SolidityAstNode } from '../../services/solidity-ast.service';
import { LibraryNode } from '../../types/cpg';

export class LibraryProcessor extends BaseProcessor {
  /**
   * Check if this processor can handle the given AST node type
   */
  canProcess(nodeType: string): boolean {
    return nodeType === 'LibraryDefinition';
  }

  /**
   * Get the priority of this processor
   */
  override getPriority(): number {
    return 95; // High priority for libraries (similar to contracts)
  }

  /**
   * Process LibraryDefinition AST node
   */
  process(
    astNode: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const sourceLocation = this.context.extractSourceLocation(
      astNode,
      filePath
    );

    // Extract library information
    const functions = this.extractLibraryFunctions(astNode);
    const dependencies = this.extractDependencies(astNode);

    // Create library node
    const libraryNode: LibraryNode = {
      id: nodeId,
      type: 'LIBRARY',
      name: astNode.name || 'unknown',
      ...(sourceLocation && { sourceLocation }),
      properties: {
        functions,
        dependencies,
      },
    };

    // Create edges
    const edges = [];
    const childNodeIds: string[] = [];

    // Create CONTAINS edge from parent (if any)
    if (parentId) {
      edges.push(this.createContainsEdge(parentId, nodeId));
    }

    // Update analysis context
    this.context.updateAnalysisContext({
      // currentLibrary is not part of the standard AnalysisContext
      // but we can add it as a custom property if needed
    });

    // Mark child nodes for processing
    if (astNode.nodes) {
      astNode.nodes.forEach((_childNode, index) => {
        childNodeIds.push(`${nodeId}_child_${index}`);
      });
    }

    const result: ProcessorResult = {
      node: libraryNode,
      edges,
      childNodeIds,
    };

    this.validateResult(result);
    return result;
  }

  /**
   * Extract library functions (will be populated by child function nodes)
   */
  private extractLibraryFunctions(_astNode: SolidityAstNode): string[] {
    // Functions will be populated by child function nodes during processing
    // This is just a placeholder for now
    return [];
  }

  /**
   * Extract library dependencies
   */
  private extractDependencies(astNode: SolidityAstNode): string[] {
    const dependencies: string[] = [];

    // Extract from using directives within the library
    if (astNode.nodes) {
      astNode.nodes.forEach((node: any) => {
        if (node.nodeType === 'UsingForDirective') {
          if (node.libraryName?.name) {
            dependencies.push(node.libraryName.name);
          }
        }
      });
    }

    // Extract from import statements (if available in context)
    // Note: Imports are typically at the source unit level, but we can
    // check for any import-related information in the library context
    const nodeAny = astNode as any;
    if (nodeAny['dependencies']) {
      nodeAny['dependencies'].forEach((dep: any) => {
        if (typeof dep === 'string') {
          dependencies.push(dep);
        } else if (dep.name) {
          dependencies.push(dep.name);
        }
      });
    }

    // Extract from inheritance (if library extends other libraries)
    if (astNode.baseContracts) {
      astNode.baseContracts.forEach((base: any) => {
        if (base.baseName?.name) {
          dependencies.push(base.baseName.name);
        }
      });
    }

    // Remove duplicates
    return [...new Set(dependencies)];
  }
}

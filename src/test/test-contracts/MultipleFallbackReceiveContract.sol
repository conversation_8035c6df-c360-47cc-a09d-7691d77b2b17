// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

/**
 * Multiple Fallback/Receive Test Contract
 * Tests how multiple contracts with fallback/receive functions are handled
 */

contract TokenA {
    uint256 public balanceA;
    
    constructor() {
        balanceA = 1000;
    }
    
    function setBalanceA(uint256 _balance) external {
        balanceA = _balance;
    }
    
    // TokenA's fallback function
    fallback() external payable {
        balanceA += msg.value;
    }
    
    // TokenA's receive function  
    receive() external payable {
        balanceA += msg.value * 2;
    }
}

contract TokenB {
    uint256 public balanceB;
    
    constructor() {
        balanceB = 2000;
    }
    
    function setBalanceB(uint256 _balance) external {
        balanceB = _balance;
    }
    
    // TokenB's fallback function (different logic)
    fallback() external payable {
        balanceB += msg.value * 3;
    }
    
    // TokenB's receive function (different logic)
    receive() external payable {
        balanceB += msg.value * 4;
    }
}

contract TokenC {
    uint256 public balanceC;
    
    constructor() {
        balanceC = 3000;
    }
    
    function setBalanceC(uint256 _balance) external {
        balanceC = _balance;
    }
    
    // TokenC's fallback function (yet different logic)
    fallback() external payable {
        balanceC += msg.value * 5;
    }
    
    // TokenC's receive function (yet different logic)
    receive() external payable {
        balanceC += msg.value * 6;
    }
}

// Contract with only fallback
contract OnlyFallback {
    uint256 public value;
    
    fallback() external payable {
        value = msg.value;
    }
}

// Contract with only receive
contract OnlyReceive {
    uint256 public value;
    
    receive() external payable {
        value = msg.value;
    }
}

// Contract with neither (for comparison)
contract NoFallbackReceive {
    uint256 public value;
    
    function setValue(uint256 _value) external {
        value = _value;
    }
}

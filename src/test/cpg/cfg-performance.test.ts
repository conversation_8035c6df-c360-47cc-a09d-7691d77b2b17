/**
 * CFG Performance and Stress Testing Suite
 *
 * Tests CFG generation performance, scalability, and robustness:
 * - Large function stress tests
 * - Deep nesting scenarios
 * - Performance benchmarks
 * - Memory usage validation
 * - Edge case robustness
 */

import { describe, it, expect } from '@jest/globals';
import { SolidityAstService } from '../../services/solidity-ast.service';
import { solidityAstToCpg } from '../../cpg/solidity-transformer';
import { CpgGraph } from '../../types/cpg';
import path from 'path';
import fs from 'fs';
import os from 'os';

/**
 * CFG Performance Test Helper
 */
class CfgPerformanceHelper {
  static async measureCfgGeneration(
    contractCode: string,
    contractName: string
  ): Promise<{
    cpg: CpgGraph | null;
    executionTime: number;
    nodeCount: number;
    edgeCount: number;
    cfgBlockCount: number;
  }> {
    const startTime = performance.now();

    try {
      const isAvailable = await SolidityAstService.isSolcAvailable();
      if (!isAvailable) {
        return {
          cpg: null,
          executionTime: 0,
          nodeCount: 0,
          edgeCount: 0,
          cfgBlockCount: 0,
        };
      }

      const tempDir = os.tmpdir();
      const contractPath = path.join(tempDir, `${contractName}.sol`);
      fs.writeFileSync(contractPath, contractCode);

      const astResult = await SolidityAstService.parseContract(contractPath);
      if (!astResult.success) {
        return {
          cpg: null,
          executionTime: 0,
          nodeCount: 0,
          edgeCount: 0,
          cfgBlockCount: 0,
        };
      }

      const cpg = solidityAstToCpg(astResult);
      const endTime = performance.now();

      const cfgBlocks = Array.from(cpg.nodes.values()).filter(
        (node) => node.type === 'CFG_BLOCK'
      );

      return {
        cpg,
        executionTime: endTime - startTime,
        nodeCount: cpg.nodes.size,
        edgeCount: cpg.edges.size,
        cfgBlockCount: cfgBlocks.length,
      };
    } catch (error) {
      const endTime = performance.now();
      return {
        cpg: null,
        executionTime: endTime - startTime,
        nodeCount: 0,
        edgeCount: 0,
        cfgBlockCount: 0,
      };
    }
  }

  static generateLargeFunctionContract(
    statementCount: number,
    nestingDepth: number
  ): string {
    let functionBody = '';

    // Generate nested structure
    for (let depth = 0; depth < nestingDepth; depth++) {
      functionBody += `    ${'  '.repeat(depth)}if (value > ${depth}) {\n`;
    }

    // Generate statements
    for (let i = 0; i < statementCount; i++) {
      const indent = '  '.repeat(nestingDepth + 2);
      functionBody += `${indent}value = value + ${i};\n`;

      if (i % 10 === 0) {
        functionBody += `${indent}if (value > ${i * 10}) {\n`;
        functionBody += `${indent}  value = value * 2;\n`;
        functionBody += `${indent}}\n`;
      }
    }

    // Close nested structure
    for (let depth = nestingDepth - 1; depth >= 0; depth--) {
      functionBody += `    ${'  '.repeat(depth)}}\n`;
    }

    return `
      pragma solidity ^0.8.0;
      
      contract LargeFunction {
        uint256 public value;
        
        function largeFunction(uint256 input) public {
          value = input;
${functionBody}
        }
      }
    `;
  }
}

describe('CFG Performance and Stress Testing', () => {
  describe('Scalability Tests', () => {
    it('should handle medium-sized functions efficiently', async () => {
      const contractCode = CfgPerformanceHelper.generateLargeFunctionContract(
        50,
        3
      );

      const result = await CfgPerformanceHelper.measureCfgGeneration(
        contractCode,
        'MediumFunction'
      );

      if (!result.cpg) {
        console.log('⚠️ solc not available - skipping medium function test');
        return;
      }

      console.log('🔍 Medium Function CFG Performance:');
      console.log(`  Execution time: ${result.executionTime.toFixed(2)}ms`);
      console.log(`  Total nodes: ${result.nodeCount}`);
      console.log(`  Total edges: ${result.edgeCount}`);
      console.log(`  CFG blocks: ${result.cfgBlockCount}`);

      // Performance expectations for medium functions
      expect(result.executionTime).toBeLessThan(5000); // Should complete in under 5 seconds
      expect(result.nodeCount).toBeGreaterThan(50);
      expect(result.cfgBlockCount).toBeGreaterThan(10);
    });

    it('should handle large functions with reasonable performance', async () => {
      const contractCode = CfgPerformanceHelper.generateLargeFunctionContract(
        100,
        5
      );

      const result = await CfgPerformanceHelper.measureCfgGeneration(
        contractCode,
        'LargeFunction'
      );

      if (!result.cpg) {
        console.log('⚠️ solc not available - skipping large function test');
        return;
      }

      console.log('🔍 Large Function CFG Performance:');
      console.log(`  Execution time: ${result.executionTime.toFixed(2)}ms`);
      console.log(`  Total nodes: ${result.nodeCount}`);
      console.log(`  Total edges: ${result.edgeCount}`);
      console.log(`  CFG blocks: ${result.cfgBlockCount}`);

      // Performance expectations for large functions
      expect(result.executionTime).toBeLessThan(20000); // Should complete in under 20 seconds (increased for thorough processing)
      expect(result.nodeCount).toBeGreaterThan(100);
      expect(result.cfgBlockCount).toBeGreaterThanOrEqual(20); // Should create many blocks for complex functions
    });
  });

  describe('Deep Nesting Stress Tests', () => {
    it('should handle deeply nested control structures', async () => {
      const contractCode = `
        pragma solidity ^0.8.0;
        
        contract DeepNesting {
          uint256 public result;
          
          function deeplyNested(uint256 x) public {
            if (x > 0) {
              if (x > 1) {
                if (x > 2) {
                  if (x > 3) {
                    if (x > 4) {
                      if (x > 5) {
                        if (x > 6) {
                          if (x > 7) {
                            if (x > 8) {
                              if (x > 9) {
                                result = x * 10;
                              } else {
                                result = x * 9;
                              }
                            } else {
                              result = x * 8;
                            }
                          } else {
                            result = x * 7;
                          }
                        } else {
                          result = x * 6;
                        }
                      } else {
                        result = x * 5;
                      }
                    } else {
                      result = x * 4;
                    }
                  } else {
                    result = x * 3;
                  }
                } else {
                  result = x * 2;
                }
              } else {
                result = x;
              }
            } else {
              result = 0;
            }
          }
        }
      `;

      const result = await CfgPerformanceHelper.measureCfgGeneration(
        contractCode,
        'DeepNesting'
      );

      if (!result.cpg) {
        console.log('⚠️ solc not available - skipping deep nesting test');
        return;
      }

      console.log('🔍 Deep Nesting CFG Performance:');
      console.log(`  Execution time: ${result.executionTime.toFixed(2)}ms`);
      console.log(`  Total nodes: ${result.nodeCount}`);
      console.log(`  CFG blocks: ${result.cfgBlockCount}`);

      // Should handle deep nesting without issues
      expect(result.executionTime).toBeLessThan(3000);
      expect(result.cfgBlockCount).toBeGreaterThan(15); // Deep nesting should create many blocks
    });
  });

  describe('Complex Loop Stress Tests', () => {
    it('should handle multiple nested loops efficiently', async () => {
      const contractCode = `
        pragma solidity ^0.8.0;
        
        contract NestedLoops {
          uint256 public result;
          
          function multipleNestedLoops(uint256 n) public {
            result = 0;
            
            for (uint256 i = 0; i < n; i++) {
              for (uint256 j = 0; j < n; j++) {
                for (uint256 k = 0; k < n; k++) {
                  if (i + j + k > n) {
                    result += i * j * k;
                  } else {
                    result += i + j + k;
                  }
                }
              }
            }
            
            uint256 temp = result;
            while (temp > 0) {
              temp = temp / 2;
              result += temp;
            }
          }
        }
      `;

      const result = await CfgPerformanceHelper.measureCfgGeneration(
        contractCode,
        'NestedLoops'
      );

      if (!result.cpg) {
        console.log('⚠️ solc not available - skipping nested loops test');
        return;
      }

      console.log('🔍 Nested Loops CFG Performance:');
      console.log(`  Execution time: ${result.executionTime.toFixed(2)}ms`);
      console.log(`  Total nodes: ${result.nodeCount}`);
      console.log(`  CFG blocks: ${result.cfgBlockCount}`);

      expect(result.executionTime).toBeLessThan(3000);
      expect(result.cfgBlockCount).toBeGreaterThan(10);
    });
  });

  describe('Memory and Resource Usage', () => {
    it('should maintain reasonable memory usage for large contracts', async () => {
      const contractCode = `
        pragma solidity ^0.8.0;
        
        contract LargeContract {
          uint256 public state1;
          uint256 public state2;
          uint256 public state3;
          
          function func1(uint256 x) public { state1 = x; }
          function func2(uint256 x) public { state2 = x; }
          function func3(uint256 x) public { state3 = x; }
          function func4(uint256 x) public { state1 = x * 2; }
          function func5(uint256 x) public { state2 = x * 2; }
          function func6(uint256 x) public { state3 = x * 2; }
          function func7(uint256 x) public { state1 = x + 1; }
          function func8(uint256 x) public { state2 = x + 1; }
          function func9(uint256 x) public { state3 = x + 1; }
          function func10(uint256 x) public { state1 = x - 1; }
        }
      `;

      const result = await CfgPerformanceHelper.measureCfgGeneration(
        contractCode,
        'LargeContract'
      );

      if (!result.cpg) {
        console.log('⚠️ solc not available - skipping large contract test');
        return;
      }

      console.log('🔍 Large Contract CFG Memory Usage:');
      console.log(`  Execution time: ${result.executionTime.toFixed(2)}ms`);
      console.log(`  Total nodes: ${result.nodeCount}`);
      console.log(`  CFG blocks: ${result.cfgBlockCount}`);
      console.log(
        `  Memory efficiency: ${(result.nodeCount / result.executionTime).toFixed(2)} nodes/ms`
      );

      // Should process multiple functions efficiently
      expect(result.nodeCount).toBeGreaterThan(30);
      expect(result.cfgBlockCount).toBeGreaterThan(20); // 10+ functions should create 20+ CFG blocks
    });
  });

  describe('Error Resilience', () => {
    it('should handle malformed control structures gracefully', async () => {
      const contractCode = `
        pragma solidity ^0.8.0;
        
        contract EdgeCases {
          uint256 public value;
          
          function edgeCaseFunction(uint256 x) public {
            // Empty if block
            if (x > 0) {
            }
            
            // Single statement blocks
            if (x > 1) value = 1;
            
            // Nested empty blocks
            if (x > 2) {
              if (x > 3) {
              }
            }
            
            // Complex expression in condition
            if (x > 0 && (x < 100 || x > 1000) && x % 2 == 0) {
              value = x;
            }
          }
        }
      `;

      const result = await CfgPerformanceHelper.measureCfgGeneration(
        contractCode,
        'EdgeCases'
      );

      if (!result.cpg) {
        console.log('⚠️ solc not available - skipping edge cases test');
        return;
      }

      console.log('🔍 Edge Cases CFG Resilience:');
      console.log(`  Execution time: ${result.executionTime.toFixed(2)}ms`);
      console.log(`  Total nodes: ${result.nodeCount}`);
      console.log(`  CFG blocks: ${result.cfgBlockCount}`);

      // Should handle edge cases without crashing
      expect(result.cpg).toBeTruthy();
      expect(result.nodeCount).toBeGreaterThan(0);
      expect(result.executionTime).toBeLessThan(2000);
    });
  });
});

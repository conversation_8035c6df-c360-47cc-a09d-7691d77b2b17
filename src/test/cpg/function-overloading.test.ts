/**
 * Function Overloading Test Suite
 * Tests comprehensive function identification, storage, and retrieval for CFG generation
 */

import path from 'path';
import { SolidityAstService } from '../../services/solidity-ast.service';
import { solidityAstToCpg } from '../../cpg/solidity-transformer';
import { FunctionRegistry } from '../../cpg/function-registry';

describe('Function Overloading and Identification', () => {
  const contractsDir = path.join(__dirname, '../test-contracts');
  const overloadingContractPath = path.join(contractsDir, 'FunctionOverloadingContract.sol');
  const advancedFeaturesPath = path.join(contractsDir, 'features', 'AdvancedFeaturesContract.sol');

  beforeAll(async () => {
    const isAvailable = await SolidityAstService.isSolcAvailable();
    if (!isAvailable) {
      console.warn('⚠️ Solc not available, skipping function overloading tests');
    }
  });

  describe('Function Registry System', () => {
    it('should create and manage function registry correctly', () => {
      const registry = new FunctionRegistry();
      
      // Test registry initialization
      expect(registry.getAllFunctions()).toHaveLength(0);
      
      // Test registry clearing
      registry.clear();
      expect(registry.getAllFunctions()).toHaveLength(0);
    });

    it('should generate unique function identifiers for overloaded functions', async () => {
      const isAvailable = await SolidityAstService.isSolcAvailable();
      if (!isAvailable) return;

      const astResult = await SolidityAstService.parseContract(overloadingContractPath);
      expect(astResult.success).toBe(true);
      if (!astResult.ast) return;

      const cpg = solidityAstToCpg(astResult);
      
      console.log('🔍 Testing Function Overloading Identification:');
      
      // Find all function nodes
      const functionNodes = Array.from(cpg.nodes.values()).filter(
        (node: any) => node.type === 'FUNCTION'
      );
      
      console.log(`  Total Functions Found: ${functionNodes.length}`);
      
      // Group functions by name to find overloads
      const functionsByName = new Map<string, any[]>();
      functionNodes.forEach((func: any) => {
        const name = func.name;
        if (!functionsByName.has(name)) {
          functionsByName.set(name, []);
        }
        functionsByName.get(name)!.push(func);
      });
      
      // Test setValue overloads
      const setValueFunctions = functionsByName.get('setValue') || [];
      console.log(`  setValue Overloads: ${setValueFunctions.length}`);
      expect(setValueFunctions.length).toBeGreaterThanOrEqual(4); // 4 setValue overloads
      
      // Verify each setValue function has unique signature
      const setValueSignatures = setValueFunctions.map((f: any) => f.properties?.signature);
      const uniqueSetValueSignatures = new Set(setValueSignatures);
      expect(uniqueSetValueSignatures.size).toBe(setValueFunctions.length);
      
      // Test process overloads
      const processFunctions = functionsByName.get('process') || [];
      console.log(`  process Overloads: ${processFunctions.length}`);
      expect(processFunctions.length).toBeGreaterThanOrEqual(4); // 4 process overloads
      
      // Test transfer overloads
      const transferFunctions = functionsByName.get('transfer') || [];
      console.log(`  transfer Overloads: ${transferFunctions.length}`);
      expect(transferFunctions.length).toBeGreaterThanOrEqual(3); // 3 transfer overloads
      
      // Test execute overloads
      const executeFunctions = functionsByName.get('execute') || [];
      console.log(`  execute Overloads: ${executeFunctions.length}`);
      expect(executeFunctions.length).toBeGreaterThanOrEqual(4); // 4 execute overloads
      
      // Verify all functions have unique IDs
      const allFunctionIds = functionNodes.map((f: any) => f.id);
      const uniqueFunctionIds = new Set(allFunctionIds);
      expect(uniqueFunctionIds.size).toBe(functionNodes.length);
      
      console.log('✅ Function overloading identification successful');
    });
  });

  describe('Special Function Identification', () => {
    it('should correctly identify constructor, fallback, and receive functions', async () => {
      const isAvailable = await SolidityAstService.isSolcAvailable();
      if (!isAvailable) return;

      const astResult = await SolidityAstService.parseContract(overloadingContractPath);
      expect(astResult.success).toBe(true);
      if (!astResult.ast) return;

      const cpg = solidityAstToCpg(astResult);
      
      console.log('🔍 Testing Special Function Identification:');
      
      const functionNodes = Array.from(cpg.nodes.values()).filter(
        (node: any) => node.type === 'FUNCTION'
      );
      
      // Find constructor
      const constructors = functionNodes.filter((f: any) => 
        f.name === 'constructor' || f.properties?.isConstructor
      );
      console.log(`  Constructors: ${constructors.length}`);
      expect(constructors.length).toBeGreaterThanOrEqual(1);
      
      // Find fallback function
      const fallbackFunctions = functionNodes.filter((f: any) => 
        f.name === 'fallback' || f.properties?.isFallback
      );
      console.log(`  Fallback Functions: ${fallbackFunctions.length}`);
      expect(fallbackFunctions.length).toBeGreaterThanOrEqual(1);
      
      // Find receive function
      const receiveFunctions = functionNodes.filter((f: any) => 
        f.name === 'receive' || f.properties?.isReceive
      );
      console.log(`  Receive Functions: ${receiveFunctions.length}`);
      expect(receiveFunctions.length).toBeGreaterThanOrEqual(1);
      
      console.log('✅ Special function identification successful');
    });

    it('should handle anonymous and unnamed functions correctly', async () => {
      const isAvailable = await SolidityAstService.isSolcAvailable();
      if (!isAvailable) return;

      const astResult = await SolidityAstService.parseContract(overloadingContractPath);
      expect(astResult.success).toBe(true);
      if (!astResult.ast) return;

      const cpg = solidityAstToCpg(astResult);
      
      const functionNodes = Array.from(cpg.nodes.values()).filter(
        (node: any) => node.type === 'FUNCTION'
      );
      
      // Ensure no functions have 'unknown' names (should be properly identified)
      const unknownFunctions = functionNodes.filter((f: any) => 
        f.name === 'unknown' || !f.name
      );
      
      console.log(`  Functions with unknown names: ${unknownFunctions.length}`);
      
      // In a well-designed contract, there should be no unknown functions
      // If there are any, they should be properly handled by the registry
      if (unknownFunctions.length > 0) {
        console.warn('⚠️ Found functions with unknown names - registry should handle these');
        unknownFunctions.forEach((f: any) => {
          console.warn(`    Unknown function: ${JSON.stringify(f, null, 2)}`);
        });
      }
      
      // All functions should have valid signatures
      const functionsWithoutSignatures = functionNodes.filter((f: any) => 
        !f.properties?.signature
      );
      
      console.log(`  Functions without signatures: ${functionsWithoutSignatures.length}`);
      expect(functionsWithoutSignatures.length).toBe(0);
    });
  });

  describe('CFG Function Lookup Integration', () => {
    it('should enable reliable function lookup for CFG generation', async () => {
      const isAvailable = await SolidityAstService.isSolcAvailable();
      if (!isAvailable) return;

      const astResult = await SolidityAstService.parseContract(overloadingContractPath);
      expect(astResult.success).toBe(true);
      if (!astResult.ast) return;

      const cpg = solidityAstToCpg(astResult);
      
      console.log('🔍 Testing CFG Function Lookup:');
      
      // Find CFG blocks
      const cfgBlocks = Array.from(cpg.nodes.values()).filter(
        (node: any) => node.type === 'CFG_BLOCK'
      );
      
      console.log(`  CFG Blocks Found: ${cfgBlocks.length}`);
      expect(cfgBlocks.length).toBeGreaterThan(0);
      
      // Find entry and exit blocks
      const entryBlocks = cfgBlocks.filter((block: any) => 
        block.properties?.blockType === 'ENTRY'
      );
      const exitBlocks = cfgBlocks.filter((block: any) => 
        block.properties?.blockType === 'EXIT'
      );
      
      console.log(`  Entry Blocks: ${entryBlocks.length}`);
      console.log(`  Exit Blocks: ${exitBlocks.length}`);
      
      // Each function should have entry and exit blocks
      expect(entryBlocks.length).toBeGreaterThan(0);
      expect(exitBlocks.length).toBeGreaterThan(0);
      
      // Check if CFG blocks have function signature information
      const blocksWithFunctionInfo = cfgBlocks.filter((block: any) => 
        block.properties?.functionSignature || block.properties?.functionId
      );
      
      console.log(`  CFG Blocks with Function Info: ${blocksWithFunctionInfo.length}`);
      
      // At least entry and exit blocks should have function information
      expect(blocksWithFunctionInfo.length).toBeGreaterThan(0);
      
      console.log('✅ CFG function lookup integration successful');
    });
  });

  describe('Advanced Features Contract Overloading', () => {
    it('should handle setValue overloads in AdvancedFeaturesContract', async () => {
      const isAvailable = await SolidityAstService.isSolcAvailable();
      if (!isAvailable) return;

      const astResult = await SolidityAstService.parseContract(advancedFeaturesPath);
      expect(astResult.success).toBe(true);
      if (!astResult.ast) return;

      const cpg = solidityAstToCpg(astResult);
      
      console.log('🔍 Testing AdvancedFeaturesContract Function Overloading:');
      
      const functionNodes = Array.from(cpg.nodes.values()).filter(
        (node: any) => node.type === 'FUNCTION'
      );
      
      // Find setValue overloads
      const setValueFunctions = functionNodes.filter((f: any) => 
        f.name === 'setValue'
      );
      
      console.log(`  setValue Overloads in AdvancedFeaturesContract: ${setValueFunctions.length}`);
      expect(setValueFunctions.length).toBeGreaterThanOrEqual(3); // 3 setValue overloads
      
      // Verify unique signatures
      const signatures = setValueFunctions.map((f: any) => f.properties?.signature);
      const uniqueSignatures = new Set(signatures.filter(s => s));
      expect(uniqueSignatures.size).toBe(setValueFunctions.length);
      
      // Log signatures for verification
      setValueFunctions.forEach((f: any, index: number) => {
        console.log(`    setValue[${index}]: ${f.properties?.signature || 'No signature'}`);
      });
      
      console.log('✅ AdvancedFeaturesContract overloading test successful');
    });
  });

  describe('Function Registry Edge Cases', () => {
    it('should handle edge cases and error conditions', () => {
      const registry = new FunctionRegistry();
      
      // Test lookup of non-existent functions
      expect(registry.getFunctionById('non-existent')).toBeUndefined();
      expect(registry.getFunctionBySignature('NonExistent.func()')).toBeUndefined();
      expect(registry.getFunctionsByContract('NonExistentContract')).toHaveLength(0);
      expect(registry.getOverloadedFunctions('Contract', 'nonExistent')).toHaveLength(0);
      expect(registry.findFunctionForCfg('Contract', 'nonExistent')).toBeUndefined();
      
      // Test with empty registry
      expect(registry.getAllFunctions()).toHaveLength(0);
      
      console.log('✅ Function registry edge cases handled correctly');
    });
  });
});

/**
 * Control Flow Graph (CFG) Processor
 * Creates CFG blocks and control flow edges for functions
 */

import { BaseProcessor, ProcessorResult } from './base-processor';
import { SolidityAstNode } from '../../services/solidity-ast.service';
import { CpgEdge, CfgBlockNode } from '../../types/cpg';

export class CfgProcessor extends BaseProcessor {
  canProcess(nodeType: string): boolean {
    // Process function bodies to create CFG blocks
    // NOTE: FunctionDefinition should be handled by FunctionProcessor, not CfgProcessor
    return nodeType === 'Block';
  }

  /**
   * Process AST node to create CFG blocks
   */
  process(
    astNode: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const sourceLocation = this.context.extractSourceLocation(
      astNode,
      filePath
    );
    const edges: CpgEdge[] = [];

    if (astNode.nodeType === 'Block') {
      return this.processBlockCfg(astNode, filePath, parentId);
    }

    // Default CFG block
    const cfgNode: CfgBlockNode = {
      id: nodeId,
      type: 'CFG_BLOCK',
      name: `cfg_block_${nodeId}`,
      ...(sourceLocation && { sourceLocation }),
      properties: {
        blockType: 'BASIC',
        statements: [],
      },
    };

    // Create CONTAINS edge from parent
    if (parentId) {
      edges.push(this.createContainsEdge(parentId, nodeId));
    }

    return {
      node: cfgNode,
      edges,
      childNodeIds: [],
    };
  }

  /**
   * Process block to create CFG blocks
   */
  private processBlockCfg(
    astNode: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const sourceLocation = this.context.extractSourceLocation(
      astNode,
      filePath
    );
    const edges: CpgEdge[] = [];

    // Extract statements from the block
    const statements = this.extractStatements(astNode);

    const cfgNode: CfgBlockNode = {
      id: nodeId,
      type: 'CFG_BLOCK',
      name: `block_${nodeId}`,
      ...(sourceLocation && { sourceLocation }),
      properties: {
        blockType: 'BASIC',
        statements,
      },
    };

    // Create CONTAINS edge from parent
    if (parentId) {
      edges.push(this.createContainsEdge(parentId, nodeId));
    }

    return {
      node: cfgNode,
      edges,
      childNodeIds: [],
    };
  }

  /**
   * Extract statements from AST node
   */
  private extractStatements(astNode: SolidityAstNode): string[] {
    const statements: string[] = [];
    const nodeAny = astNode as any;

    if (nodeAny['statements']) {
      nodeAny['statements'].forEach((stmt: any) => {
        statements.push(this.statementToString(stmt));
      });
    }

    return statements;
  }

  /**
   * Convert statement to string representation
   */
  private statementToString(stmt: any): string {
    if (typeof stmt === 'string') return stmt;
    if (stmt.nodeType) return `${stmt.nodeType}`;
    if (stmt.type) return `${stmt.type}`;
    return 'unknown_statement';
  }
}

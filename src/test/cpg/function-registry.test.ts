/**
 * Function Registry Test Suite
 * Tests the core function registry functionality without complex AST dependencies
 */

import { FunctionRegistry } from '../../cpg/function-registry';
import { FunctionNode } from '../../types/cpg';

describe('Function Registry Core Functionality', () => {
  let registry: FunctionRegistry;

  beforeEach(() => {
    registry = new FunctionRegistry();
  });

  describe('Registry Initialization', () => {
    it('should initialize empty registry correctly', () => {
      expect(registry.getAllFunctions()).toHaveLength(0);
    });

    it('should clear registry correctly', () => {
      registry.clear();
      expect(registry.getAllFunctions()).toHaveLength(0);
    });
  });

  describe('Function Registration', () => {
    it('should register a basic function correctly', () => {
      // Create mock AST node
      const mockAstNode = {
        nodeType: 'FunctionDefinition',
        name: 'testFunction',
        parameters: {
          parameters: [
            {
              typeDescriptions: { typeString: 'uint256' },
            },
          ],
        },
        visibility: 'public',
        stateMutability: 'nonpayable',
      } as any;

      // Create mock CPG node
      const mockCpgNode: FunctionNode = {
        id: 'func_1',
        type: 'FUNCTION',
        name: 'testFunction',
        properties: {
          signature: 'testFunction(uint256)',
          visibility: 'public',
          stateMutability: 'nonpayable',
          modifiers: [],
          parameters: [{ name: 'param1', type: 'uint256' }],
          returns: [],
          isVirtual: false,
          isOverride: false,
          isReceive: false,
          isFallback: false,
        },
      };

      const identifier = registry.registerFunction(
        mockAstNode,
        mockCpgNode,
        'test.sol',
        'TestContract',
        0
      );

      expect(identifier.functionName).toBe('testFunction');
      expect(identifier.contractName).toBe('TestContract');
      expect(identifier.parameterTypes).toEqual(['uint256']);
      expect(identifier.kind).toBe('function');
      expect(identifier.signature).toContain(
        'TestContract.testFunction(uint256)'
      );
    });

    it('should handle constructor functions correctly', () => {
      const mockAstNode = {
        nodeType: 'FunctionDefinition',
        name: 'constructor',
        kind: 'constructor',
        parameters: { parameters: [] },
        visibility: 'public',
        stateMutability: 'nonpayable',
      } as any;

      const mockCpgNode: FunctionNode = {
        id: 'func_constructor',
        type: 'FUNCTION',
        name: 'constructor',
        properties: {
          signature: 'constructor()',
          visibility: 'public',
          stateMutability: 'nonpayable',
          modifiers: [],
          parameters: [],
          returns: [],
          isVirtual: false,
          isOverride: false,
          isReceive: false,
          isFallback: false,
        },
      };

      const identifier = registry.registerFunction(
        mockAstNode,
        mockCpgNode,
        'test.sol',
        'TestContract',
        0
      );

      expect(identifier.kind).toBe('constructor');
      expect(identifier.functionName).toBe('constructor');
      expect(identifier.signature).toContain('constructor:constructor()');
    });

    it('should handle fallback functions correctly', () => {
      const mockAstNode = {
        nodeType: 'FunctionDefinition',
        name: 'fallback',
        kind: 'fallback',
        parameters: { parameters: [] },
        visibility: 'external',
        stateMutability: 'payable',
      } as any;

      const mockCpgNode: FunctionNode = {
        id: 'func_fallback',
        type: 'FUNCTION',
        name: 'fallback',
        properties: {
          signature: 'fallback()',
          visibility: 'external',
          stateMutability: 'payable',
          modifiers: [],
          parameters: [],
          returns: [],
          isVirtual: false,
          isOverride: false,
          isReceive: false,
          isFallback: true,
        },
      };

      const identifier = registry.registerFunction(
        mockAstNode,
        mockCpgNode,
        'test.sol',
        'TestContract',
        0
      );

      expect(identifier.kind).toBe('fallback');
      expect(identifier.functionName).toBe('fallback');
      expect(identifier.signature).toContain('fallback:fallback()');
    });

    it('should handle receive functions correctly', () => {
      const mockAstNode = {
        nodeType: 'FunctionDefinition',
        name: 'receive',
        kind: 'receive',
        parameters: { parameters: [] },
        visibility: 'external',
        stateMutability: 'payable',
      } as any;

      const mockCpgNode: FunctionNode = {
        id: 'func_receive',
        type: 'FUNCTION',
        name: 'receive',
        properties: {
          signature: 'receive()',
          visibility: 'external',
          stateMutability: 'payable',
          modifiers: [],
          parameters: [],
          returns: [],
          isVirtual: false,
          isOverride: false,
          isReceive: true,
          isFallback: false,
        },
      };

      const identifier = registry.registerFunction(
        mockAstNode,
        mockCpgNode,
        'test.sol',
        'TestContract',
        0
      );

      expect(identifier.kind).toBe('receive');
      expect(identifier.functionName).toBe('receive');
      expect(identifier.signature).toContain('receive:receive()');
    });
  });

  describe('Function Overloading', () => {
    it('should handle function overloading correctly', () => {
      // Register first overload
      const mockAstNode1 = {
        nodeType: 'FunctionDefinition',
        name: 'setValue',
        parameters: {
          parameters: [{ typeDescriptions: { typeString: 'uint256' } }],
        },
      } as any;

      const mockCpgNode1: FunctionNode = {
        id: 'func_1',
        type: 'FUNCTION',
        name: 'setValue',
        properties: {
          signature: 'setValue(uint256)',
          visibility: 'public',
          stateMutability: 'nonpayable',
          modifiers: [],
          parameters: [{ name: 'value', type: 'uint256' }],
          returns: [],
          isVirtual: false,
          isOverride: false,
          isReceive: false,
          isFallback: false,
        },
      };

      // Register second overload
      const mockAstNode2 = {
        nodeType: 'FunctionDefinition',
        name: 'setValue',
        parameters: {
          parameters: [
            { typeDescriptions: { typeString: 'uint256' } },
            { typeDescriptions: { typeString: 'string' } },
          ],
        },
      } as any;

      const mockCpgNode2: FunctionNode = {
        id: 'func_2',
        type: 'FUNCTION',
        name: 'setValue',
        properties: {
          signature: 'setValue(uint256,string)',
          visibility: 'public',
          stateMutability: 'nonpayable',
          modifiers: [],
          parameters: [
            { name: 'value', type: 'uint256' },
            { name: 'text', type: 'string' },
          ],
          returns: [],
          isVirtual: false,
          isOverride: false,
          isReceive: false,
          isFallback: false,
        },
      };

      const id1 = registry.registerFunction(
        mockAstNode1,
        mockCpgNode1,
        'test.sol',
        'TestContract',
        0
      );
      const id2 = registry.registerFunction(
        mockAstNode2,
        mockCpgNode2,
        'test.sol',
        'TestContract',
        0
      );

      // Should have different IDs and signatures
      expect(id1.id).not.toBe(id2.id);
      expect(id1.signature).not.toBe(id2.signature);
      expect(id1.parameterTypes).toEqual(['uint256']);
      expect(id2.parameterTypes).toEqual(['uint256', 'string']);

      // Should find both overloads
      const overloads = registry.getOverloadedFunctions(
        'TestContract',
        'setValue'
      );
      expect(overloads).toHaveLength(2);
    });
  });

  describe('Function Lookup', () => {
    beforeEach(() => {
      // Register a test function
      const mockAstNode = {
        nodeType: 'FunctionDefinition',
        name: 'testFunction',
        parameters: {
          parameters: [{ typeDescriptions: { typeString: 'uint256' } }],
        },
      } as any;

      const mockCpgNode: FunctionNode = {
        id: 'func_test',
        type: 'FUNCTION',
        name: 'testFunction',
        properties: {
          signature: 'testFunction(uint256)',
          visibility: 'public',
          stateMutability: 'nonpayable',
          modifiers: [],
          parameters: [{ name: 'param1', type: 'uint256' }],
          returns: [],
          isVirtual: false,
          isOverride: false,
          isReceive: false,
          isFallback: false,
        },
      };

      registry.registerFunction(
        mockAstNode,
        mockCpgNode,
        'test.sol',
        'TestContract',
        0
      );
    });

    it('should find function by ID', () => {
      const allFunctions = registry.getAllFunctions();
      expect(allFunctions).toHaveLength(1);

      const firstFunction = allFunctions[0];
      expect(firstFunction).toBeDefined();

      const functionId = firstFunction!.identifier.id;
      const found = registry.getFunctionById(functionId);

      expect(found).toBeDefined();
      expect(found?.identifier.functionName).toBe('testFunction');
    });

    it('should find function by signature', () => {
      const allFunctions = registry.getAllFunctions();
      expect(allFunctions).toHaveLength(1);

      const firstFunction = allFunctions[0];
      expect(firstFunction).toBeDefined();

      const signature = firstFunction!.identifier.signature;
      const found = registry.getFunctionBySignature(signature);

      expect(found).toBeDefined();
      expect(found?.identifier.functionName).toBe('testFunction');
    });

    it('should find functions by contract', () => {
      const functions = registry.getFunctionsByContract('TestContract');

      expect(functions).toHaveLength(1);

      const firstFunction = functions[0];
      expect(firstFunction).toBeDefined();
      expect(firstFunction!.identifier.functionName).toBe('testFunction');
    });

    it('should find function for CFG generation', () => {
      const found = registry.findFunctionForCfg(
        'TestContract',
        'testFunction',
        ['uint256']
      );

      expect(found).toBeDefined();
      expect(found?.identifier.functionName).toBe('testFunction');
    });

    it('should return undefined for non-existent functions', () => {
      expect(registry.getFunctionById('non-existent')).toBeUndefined();
      expect(
        registry.getFunctionBySignature('NonExistent.func()')
      ).toBeUndefined();
      expect(
        registry.getFunctionsByContract('NonExistentContract')
      ).toHaveLength(0);
      expect(
        registry.findFunctionForCfg('Contract', 'nonExistent')
      ).toBeUndefined();
    });
  });
});

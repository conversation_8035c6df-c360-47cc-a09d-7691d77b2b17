/**
 * Function Processor
 * Handles FunctionDefinition AST nodes
 */

import { SolidityAstNode } from '../../services/solidity-ast.service';
import { FunctionNode, ModifiesEdge } from '../../types/cpg';
import { BaseProcessor, ProcessorResult } from './base-processor';

export class FunctionProcessor extends BaseProcessor {
  /**
   * Check if this processor can handle the given AST node type
   */
  canProcess(nodeType: string): boolean {
    return nodeType === 'FunctionDefinition';
  }

  /**
   * Get the priority of this processor
   */
  override getPriority(): number {
    return 80;
  }

  /**
   * Process FunctionDefinition AST node
   */
  process(
    astNode: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const sourceLocation = this.context.extractSourceLocation(
      astNode,
      filePath
    );

    // Get contract name from analysis context for enhanced identification
    const analysisContext = this.context.getAnalysisContext();
    const contractName = analysisContext.currentContract || 'UnknownContract';
    const inheritanceLevel = analysisContext.inheritanceLevel || 0;

    // Extract function information
    const parameters = this.extractParameters(astNode.parameters);
    const returns = this.extractParameters(astNode.returnParameters);
    const modifiers = this.extractModifiers(astNode);

    // Phase 1: Critical Missing Features Detection
    const isReceive = this.isReceiveFunction(astNode);
    const isFallback = this.isFallbackFunction(astNode);
    const isVirtual = this.isVirtualFunction(astNode);
    const isOverride = this.isOverrideFunction(astNode);

    // Determine function name with proper handling for special functions
    let functionName = astNode.name;
    if (!functionName) {
      if (astNode['kind'] === 'constructor') {
        functionName = 'constructor';
      } else if (isFallback) {
        functionName = 'fallback';
      } else if (isReceive) {
        functionName = 'receive';
      } else {
        functionName = 'unknown';
      }
    }

    // Create function node with temporary signature (will be updated by registry)
    const functionNode: FunctionNode = {
      id: nodeId,
      type: 'FUNCTION',
      name: functionName,
      ...(sourceLocation && { sourceLocation }),
      properties: {
        signature: 'temp_signature', // Will be updated by registry
        visibility: astNode.visibility || 'internal',
        stateMutability: astNode.stateMutability || 'nonpayable',
        modifiers: modifiers.map((m) => m.name),
        parameters,
        returns,
        isVirtual,
        isOverride,
        isReceive,
        isFallback,
      },
    };

    // Create edges
    const edges = [];
    const childNodeIds: string[] = [];

    // Create CONTAINS edge from parent (contract)
    if (parentId) {
      edges.push(this.createContainsEdge(parentId, nodeId));
    }

    // Create MODIFIES edges for function modifiers
    const modifierEdges = this.createModifierEdges(nodeId, modifiers);
    edges.push(...modifierEdges);

    // Register function in the enhanced registry
    const functionRegistry = this.context.getFunctionRegistry();
    const functionIdentifier = functionRegistry.registerFunction(
      { ...astNode, name: functionName }, // Use determined function name
      functionNode,
      filePath,
      contractName,
      inheritanceLevel
    );

    // Update function node with registry's enhanced signature
    functionNode.properties.signature = functionIdentifier.signature;

    // Update analysis context with enhanced function information
    this.context.updateAnalysisContext({
      currentFunction: functionIdentifier.signature,
      currentFunctionId: functionIdentifier.id,
      currentFunctionSignature: functionIdentifier.signature,
    });

    // Mark child nodes for processing
    this.markChildNodesForProcessing(astNode, nodeId, childNodeIds);

    const result: ProcessorResult = {
      node: functionNode,
      edges,
      childNodeIds,
    };

    this.validateResult(result);
    return result;
  }

  /**
   * Extract modifier information from function AST node
   */
  private extractModifiers(
    astNode: SolidityAstNode
  ): Array<{ name: string; arguments?: any[] }> {
    if (!astNode.modifiers || !Array.isArray(astNode.modifiers)) {
      return [];
    }

    return astNode.modifiers.map((modifier: any) => ({
      name: modifier.modifierName?.name || modifier.name || 'unknown',
      arguments: modifier.arguments || [],
    }));
  }

  /**
   * Create MODIFIES edges for function modifiers
   */
  private createModifierEdges(
    functionId: string,
    modifiers: Array<{ name: string; arguments?: any[] }>
  ): ModifiesEdge[] {
    return modifiers.map((modifier) => ({
      id: this.context.generateEdgeId(),
      type: 'MODIFIES',
      source: functionId,
      target: `modifier_${modifier.name}`, // Will be resolved later
      properties: {
        modifierName: modifier.name,
      },
    }));
  }

  /**
   * Mark child nodes for processing
   */
  private markChildNodesForProcessing(
    astNode: SolidityAstNode,
    parentId: string,
    childNodeIds: string[]
  ): void {
    // Process parameters
    if (astNode.parameters?.parameters) {
      astNode.parameters.parameters['forEach']((_param: any, index: number) => {
        childNodeIds.push(`${parentId}_param_${index}`);
      });
    }

    // Process return parameters
    if (astNode.returnParameters?.parameters) {
      astNode.returnParameters.parameters['forEach'](
        (_param: any, index: number) => {
          childNodeIds.push(`${parentId}_return_${index}`);
        }
      );
    }

    // Process function body
    if (astNode.body) {
      childNodeIds.push(`${parentId}_body`);
    }

    // Process modifiers
    if (astNode['modifiers']) {
      astNode['modifiers']['forEach']((_modifier: any, index: number) => {
        childNodeIds.push(`${parentId}_modifier_${index}`);
      });
    }
  }

  /**
   * Phase 1: Critical Missing Features Detection Methods
   */

  /**
   * Check if function is a receive function
   */
  private isReceiveFunction(astNode: SolidityAstNode): boolean {
    // Primary check: explicit receive function markers
    return astNode['kind'] === 'receive' || astNode.name === 'receive';
  }

  /**
   * Check if function is a fallback function
   */
  private isFallbackFunction(astNode: SolidityAstNode): boolean {
    // Primary check: explicit fallback function markers
    return astNode['kind'] === 'fallback' || astNode.name === 'fallback';
  }

  /**
   * Check if function is virtual
   */
  private isVirtualFunction(astNode: SolidityAstNode): boolean {
    return Boolean(
      astNode['virtual'] === true ||
        (astNode.modifiers &&
          astNode.modifiers.some((m: any) => m.name === 'virtual'))
    );
  }

  /**
   * Check if function is an override
   */
  private isOverrideFunction(astNode: SolidityAstNode): boolean {
    const funcName = astNode.name || 'unknown';

    // Debug logging for functions that should be overrides
    if (
      funcName.includes('override') ||
      funcName === 'abstractFunction' ||
      funcName === 'virtualFunction' ||
      funcName === 'interfaceFunction'
    ) {
      console.log(`🔍 DEBUG Override check for ${funcName}:`, {
        override: astNode['override'],
        overrides: astNode['overrides'],
        modifiers: astNode.modifiers?.map((m: any) => m.name),
        baseFunctions: astNode['baseFunctions'],
      });
    }

    // Check for override keyword in AST
    if (astNode['override'] !== undefined && astNode['override'] !== null) {
      console.log(
        `🔍 DEBUG: Override found via override property: ${funcName}`
      );
      return true;
    }

    // Check modifiers for override
    if (
      astNode.modifiers &&
      astNode.modifiers.some((m: any) => m.name === 'override')
    ) {
      console.log(`🔍 DEBUG: Override found via modifiers: ${funcName}`);
      return true;
    }

    // Check overrides array
    if (astNode['overrides'] && astNode['overrides'].length > 0) {
      console.log(`🔍 DEBUG: Override found via overrides array: ${funcName}`);
      return true;
    }

    // Check baseFunctions (indicates this function overrides something)
    if (astNode['baseFunctions'] && astNode['baseFunctions'].length > 0) {
      console.log(`🔍 DEBUG: Override found via baseFunctions: ${funcName}`);
      return true;
    }

    return false;
  }
}
